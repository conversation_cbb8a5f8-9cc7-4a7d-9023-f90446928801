/**
 * <PERSON><PERSON><PERSON><PERSON> DOS DASHBOARDS - PORTAL BETINA
 * Organização centralizada de todos os dashboards
 *
 * ESTRUTURA:
 * - TODOS OS DASHBOARDS SÃO PREMIUM (Pagos)
 * - 1 Dashboard ADMIN (Sistema Integrado)
 * - 4 Dashboards PREMIUM (Performance, IA, Neuropedagógico, Multissensorial)
 */

// 🎯 DASHBOARDS PREMIUM (Requerem assinatura)
export { default as PerformanceDashboard } from './PerformanceDashboard'

// 💎 DASHBOARDS PREMIUM (Requerem assinatura)
export { default as AdvancedAIReport } from './AdvancedAIReport' // RELATÓRIO A
export { default as RelatorioADashboard } from './RelatorioADashboard' // RELATÓRIO A (Legacy)
export { default as NeuropedagogicalDashboard } from './NeuropedagogicalDashboard'
export { default as BackupExportDashboard } from './BackupExportDashboard' // BACKUP E EXPORTAÇÃO

// 🎨 COMPONENTES DE LAYOUT E NAVEGAÇÃO
export { default as DashboardLayout } from './DashboardLayout/DashboardLayout'
export { default as DashboardNavigation } from './DashboardNavigation/DashboardNavigation'

// 📊 DASHBOARDS ADICIONAIS
export { default as RealTimeDashboard } from './RealTimeDashboard/RealTimeDashboard'
export { default as DashboardContainer } from './DashboardContainer'
export { default as DashboardWrapper } from './DashboardWrapper'
export { default as DashboardPremiumContainer } from './DashboardPremiumContainer'

/**
 * CONFIGURAÇÃO DE ACESSO DOS DASHBOARDS
 */
export const DASHBOARD_CONFIG = {
  // Dashboard Performance - AGORA PREMIUM
  performance: {
    component: 'PerformanceDashboard',
    title: 'Performance Dashboard',
    description: 'Métricas avançadas de performance e uso',
    access: 'premium',
    icon: '📊',
    features: ['Accuracy detalhada', 'Tempo de sessão', 'Pontuação avançada', 'Progresso completo']
  },

  // Dashboards premium
  relatorioA: {
    component: 'AdvancedAIReport',
    title: 'Relatório A - Análise IA',
    description: 'Análise avançada com Inteligência Artificial',
    access: 'premium',
    icon: '🤖',
    features: [
      'Análise cognitiva IA',
      'Padrões comportamentais',
      'Previsões desenvolvimento',
      'Mapeamento neural'
    ]
  },

  neuropedagogical: {
    component: 'NeuropedagogicalDashboard',
    title: 'Dashboard Neuropedagógico',
    description: 'Métricas especializadas para terapeutas',
    access: 'premium',
    icon: '🧠',
    features: [
      'Função executiva',
      'Atenção sustentada',
      'Processamento sensorial',
      'Relatórios profissionais'
    ]
  },

  backupExport: {
    component: 'BackupExportDashboard',
    title: 'Backup e Exportação',
    description: 'Gerencie seus dados com recursos avançados',
    access: 'premium',
    icon: '💾',
    features: [
      'Backup automático',
      'Sincronização na nuvem',
      'Criptografia avançada',
      'Análise de dados'
    ]
  }
}

/**
 * UTILITÁRIOS PARA CONTROLE DE ACESSO
 */
export const getDashboardsByAccess = (accessLevel, isAdmin = false) => {
  return Object.entries(DASHBOARD_CONFIG).filter(([key, config]) => {
    // Verificar acesso admin
    if (config.access === 'admin') {
      return isAdmin
    }
    
    // TODOS OS DASHBOARDS SÃO PREMIUM AGORA
    if (accessLevel === 'premium') {
      return config.access === 'premium'
    }
    
    // Sem acesso público - todos requerem premium
    return false
  })
}

export const isPremiumDashboard = (dashboardKey) => {
  return DASHBOARD_CONFIG[dashboardKey]?.access === 'premium'
}

export const isAdminDashboard = (dashboardKey) => {
  return DASHBOARD_CONFIG[dashboardKey]?.access === 'admin'
}

export const canAccessDashboard = (dashboardKey, accessLevel, isAdmin = false) => {
  const config = DASHBOARD_CONFIG[dashboardKey]
  if (!config) return false
  
  if (config.access === 'admin') {
    return isAdmin
  }
  
  if (config.access === 'premium') {
    return accessLevel === 'premium'
  }
  
  return true // Público
}

export const getDashboardConfig = (dashboardKey) => {
  return DASHBOARD_CONFIG[dashboardKey]
}

/**
 * LISTA ORDENADA DOS DASHBOARDS
 */
export const DASHBOARD_ORDER = [
  'performance', // Sempre primeiro (público)
  'relatorioA', // Relatório A - IA
  'neuropedagogical', // Neuropedagógico
  'backupExport', // Backup e Exportação
  'multisensory' // Multissensorial
]
