/**
 * @file ai-reports.js
 * @description Rotas de API para relatórios e análises com IA
 * @version 1.1.0 - Melhorada com autenticação, validação e logging
 */

import express from 'express';
import { globalRateLimit, strictRateLimit, premiumRateLimit } from '../middleware/security/rateLimiter.js';
import { validateInput, schemas } from '../../middleware/validation/inputValidator.js';
import { asyncHandler, createError } from '../../middleware/error/errorHandler.js';
import { authenticate } from '../../middleware/auth/jwt.js';
import { sanitizeInput, sanitizeQuery } from '../../middleware/security/sanitization.js';
import { createIntegratedSystem } from '../services/createIntegratedSystem.js';

const router = express.Router();

/**
 * Middleware para inicializar o sistema integrado
 */
async function initializeSystem(req, res, next) {
  try {
    if (!req.app.locals.integratedSystem) {
      req.app.locals.integratedSystem = await createIntegratedSystem();
      await req.app.locals.integratedSystem.initialize();
    }
    req.integratedSystem = req.app.locals.integratedSystem;
    next();
  } catch (error) {
    console.error('❌ Erro ao inicializar sistema integrado:', {
      error: error.message,
      stack: error.stack
    });
    throw createError(500, 'Erro interno ao inicializar sistema', { details: error.message });
  }
}

/**
 * Middleware para validar parâmetros de entrada
 */
function validateParams(requiredParams) {
  return (req, res, next) => {
    const missingParams = requiredParams.filter(
      param => !req.body[param] && !req.params[param] && !req.query[param]
    );

    if (missingParams.length > 0) {
      throw createError(400, 'Parâmetros obrigatórios ausentes', { missingParams });
    }

    next();
  };
}

/**
 * POST /api/ai-reports/generate
 * Gera relatório personalizado com IA
 */
router.post(
  '/generate',
  authenticate,
  globalRateLimit,
  sanitizeInput,
  validateInput(schemas.genericData),
  initializeSystem,
  validateParams(['childId', 'parentId']),
  asyncHandler(async (req, res) => {
    const { childId, parentId, reportType, timeRange, language, focus } = req.body;

    console.info(`📊 Gerando relatório AI`, {
      childId,
      reportType,
      timeRange,
      language
    });

    const result = await req.integratedSystem.generateAIReport(childId, parentId, {
      reportType: reportType || 'comprehensive',
      timeRange: Number(timeRange) || 7,
      language: language || 'pt-BR',
      focus: focus || 'development'
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      service: 'ai-reports'
    });
  })
);

/**
 * POST /api/ai-reports/analyze-metrics
 * Analisa métricas de jogo com IA
 */
router.post(
  '/analyze-metrics',
  authenticate,
  globalRateLimit,
  sanitizeInput,
  validateInput(schemas.genericData),
  initializeSystem,
  validateParams(['userId', 'gameId', 'metrics']),
  asyncHandler(async (req, res) => {
    const { userId, gameId, metrics } = req.body;

    console.info(`🧠 Analisando métricas com IA`, {
      userId,
      gameId,
      metricsCount: metrics?.length
    });

    if (!Array.isArray(metrics)) {
      throw createError(400, 'Métricas devem ser um array');
    }

    const result = await req.integratedSystem.processGameMetricsWithAI(userId, gameId, metrics);

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      service: 'ai-reports'
    });
  })
);

/**
 * GET /api/ai-reports/insights/:childId
 * Obtém insights preditivos para uma criança
 */
router.get(
  '/insights/:childId',
  authenticate,
  sanitizeQuery,
  initializeSystem,
  validateParams(['childId']),
  asyncHandler(async (req, res) => {
    const { childId } = req.params;
    const { timeRange, predictionType, timeframe, focus } = req.query;

    console.info(`🔮 Gerando insights preditivos`, {
      childId,
      timeRange,
      predictionType
    });

    const result = await req.integratedSystem.getPredictiveInsights(childId, {
      timeRange: Number(timeRange) || 30,
      predictionType: predictionType || 'development',
      timeframe: timeframe || 'next_week',
      focus: focus || 'improvement_areas'
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      service: 'ai-reports'
    });
  })
);

/**
 * POST /api/ai-reports/dashboard
 * Obtém dados de dashboard consolidados com IA
 */
router.post(
  '/dashboard',
  authenticate,
  globalRateLimit,
  sanitizeInput,
  validateInput(schemas.genericData),
  initializeSystem,
  validateParams(['parentId', 'childIds']),
  asyncHandler(async (req, res) => {
    const { parentId, childIds, timeRange, focusAreas } = req.body;

    console.info(`📈 Gerando dashboard`, {
      parentId,
      childCount: childIds?.length,
      timeRange
    });

    if (!Array.isArray(childIds)) {
      throw createError(400, 'childIds deve ser um array');
    }

    const result = await req.integratedSystem.getDashboardData(parentId, childIds, {
      timeRange: Number(timeRange) || 7,
      focusAreas: focusAreas || ['engagement', 'progress', 'challenges']
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      service: 'ai-reports'
    });
  })
);

/**
 * GET /api/ai-reports/health
 * Verifica saúde do sistema AI
 */
router.get(
  '/health',
  initializeSystem,
  asyncHandler(async (req, res) => {
    const systemHealth = await req.integratedSystem.healthCheck();

    const aiServiceHealth = {
      available: !!req.integratedSystem.aiService,
      configured: !!(process.env.AI_API_KEY && process.env.AI_API_ENDPOINT),
      status: req.integratedSystem.aiService ? 'ready' : 'unavailable'
    };

    res.json({
      success: true,
      data: {
        system: systemHealth,
        aiService: aiServiceHealth,
        timestamp: new Date().toISOString()
      },
      service: 'ai-reports'
    });
  })
);

/**
 * GET /api/ai-reports/config
 * Retorna configuração do sistema AI (sem dados sensíveis)
 */
router.get(
  '/config',
  initializeSystem,
  asyncHandler(async (req, res) => {
    const config = {
      supportedGames: req.integratedSystem.aiService?.supportedGames || [],
      cognitiveDomains: req.integratedSystem.aiService?.cognitiveDomains || {},
      reportTypes: ['comprehensive', 'progress', 'challenges', 'recommendations'],
      languages: ['pt-BR', 'en-US'],
      focusAreas: ['development', 'engagement', 'progress', 'challenges', 'improvement_areas'],
      timeRanges: [7, 14, 30, 90],
      predictionTypes: ['development', 'engagement', 'improvement', 'challenges'],
      timeframes: ['next_week', 'next_month', 'next_quarter']
    };

    res.json({
      success: true,
      data: config,
      timestamp: new Date().toISOString(),
      service: 'ai-reports'
    });
  })
);

export default router;