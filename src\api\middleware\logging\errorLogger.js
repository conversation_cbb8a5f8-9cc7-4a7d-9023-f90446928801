/**
 * Error Logger Middleware
 * Portal Betina V3 - Sistema Terapêutico para Neurodivergência
 *
 * Middleware responsável por registrar erros detalhados do sistema
 * para análise e debugging em ambiente terapêutico.
 */

import path from 'path'
import winston from 'winston'

// Configuração do logger de erros
const errorLogger = winston.createLogger({
  level: 'error',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level,
        message,
        stack,
        ...meta
      }, null, 2)
    })
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'errors.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
})

/**
 * Middleware para registrar erros detalhados
 * Captura informações contextuais para debugging terapêutico
 *
 * @param {Error} err - Erro capturado
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
const logError = (err, req, res, next) => {
  try {
    const errorInfo = {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
        code: err.code || 'UNKNOWN_ERROR'
      },
      request: {
        method: req.method,
        url: req.originalUrl,
        headers: {
          'user-agent': req.get('User-Agent'),
          'content-type': req.get('Content-Type'),
          authorization: req.get('Authorization') ? 'Bearer [REDACTED]' : undefined
        },
        body: sanitizeRequestBody(req.body),
        params: req.params,
        query: req.query,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      },
      user: {
        id: req.user?.id || 'anonymous',
        role: req.user?.role || 'unknown',
        sessionId: req.sessionID
      },
      therapeutic: {
        childId: req.body?.childId || req.params?.childId,
        sessionId: req.body?.sessionId || req.params?.sessionId,
        gameId: req.body?.gameId || req.params?.gameId
      },
      system: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime()
      }
    }

    errorLogger.error('Application Error', errorInfo)

    // Em produção, também enviar para serviço de monitoramento
    if (process.env.NODE_ENV === 'production') {
      // TODO: Integrar com serviço de monitoramento (Sentry, DataDog, etc.)
      sendToMonitoringService(errorInfo)
    }
  } catch (logError) {
    console.error('Erro ao registrar erro:', logError)
  }

  next(err)
}

/**
 * Remove dados sensíveis do body da requisição
 *
 * @param {Object} body - Request body
 * @returns {Object} Sanitized body
 */
function sanitizeRequestBody (body) {
  if (!body || typeof body !== 'object') return body

  const sensitiveFields = ['password', 'token', 'secret', 'key', 'apiKey']
  const sanitized = { ...body }

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]'
    }
  })

  return sanitized
}

/**
 * Envia erro para serviço de monitoramento externo
 *
 * @param {Object} errorInfo - Informações do erro
 */
function sendToMonitoringService (errorInfo) {
  // Implementação futura para integração com serviços de monitoramento
  // como Sentry, DataDog, New Relic, etc.
  console.log('Enviando erro para serviço de monitoramento:', errorInfo.error.message)
}

/**
 * Logger específico para erros terapêuticos
 * Registra falhas em sessões, jogos e análises
 *
 * @param {Object} therapeuticError - Erro terapêutico específico
 */
const logTherapeuticError = (therapeuticError) => {
  const therapeuticLog = {
    type: 'THERAPEUTIC_ERROR',
    timestamp: new Date().toISOString(),
    childId: therapeuticError.childId,
    sessionId: therapeuticError.sessionId,
    gameId: therapeuticError.gameId,
    errorType: therapeuticError.errorType,
    description: therapeuticError.description,
    impact: therapeuticError.impact,
    context: therapeuticError.context
  }

  errorLogger.error('Therapeutic System Error', therapeuticLog)
}

/**
 * Logger para erros críticos do sistema
 * Erros que podem afetar múltiplos usuários ou sessões
 *
 * @param {Object} criticalError - Erro crítico
 */
const logCriticalError = (criticalError) => {
  const criticalLog = {
    type: 'CRITICAL_ERROR',
    timestamp: new Date().toISOString(),
    severity: 'CRITICAL',
    component: criticalError.component,
    error: criticalError.error,
    affectedUsers: criticalError.affectedUsers || [],
    systemImpact: criticalError.systemImpact,
    recoveryAction: criticalError.recoveryAction
  }

  errorLogger.error('Critical System Error', criticalLog)

  // Notificar administradores imediatamente
  if (process.env.NODE_ENV === 'production') {
    // TODO: Implementar notificação de emergência
    console.error('CRITICAL ERROR - Immediate attention required:', criticalLog)
  }
}

export {
  logError,
  logTherapeuticError,
  logCriticalError,
  errorLogger
}

export default errorLogger
