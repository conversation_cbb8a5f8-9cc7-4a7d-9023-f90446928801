import { useState, useEffect, useCallback, useRef } from 'react';
import { MultisensoryMetricsCollector } from '../api/services/multisensoryAnalysis/multisensoryMetrics.js';

// 📝 Sistema de Logs Estruturados
import { StructuredLogger } from '../api/services/core/logging/StructuredLogger.js';

/**
 * Hook para integração multissensorial em jogos
 * 
 * REFATORADO v3.1: Agora usa MultisensoryMetricsCollector diretamente,
 * eliminando a camada intermediária redundante do GameSensorIntegrator.
 * 
 * @param {string} gameType - Tipo do jogo (ex: 'color-match', 'memory-game', etc.)
 * @param {Object} collectors - Coletores específicos do jogo (opcional)
 * @param {Object} options - Opções de configuração
 * @returns {Object} Interface do hook multissensorial
 */
export function useMultisensoryIntegration(gameType, collectors, options = {}) {
  // 📝 Logger estruturado para o hook
  const [logger] = useState(() => StructuredLogger.getInstance({
    serviceName: 'useMultisensoryIntegration',
    logLevel: options.logLevel || 'info'
  }));

  const [multisensoryCollector] = useState(() => new MultisensoryMetricsCollector(gameType));
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentSession, setCurrentSession] = useState(null);
  const [multisensoryData, setMultisensoryData] = useState(null);
  const [sessionReport, setSessionReport] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const lastUpdateRef = useRef(Date.now());
  const updateIntervalRef = useRef(null);
  const sessionIdRef = useRef(null);

  // Configurações padrão
  const defaultOptions = {
    autoUpdate: true,
    updateInterval: 1000, // 1 segundo
    enablePatternAnalysis: true,
    logLevel: 'info',
    enableSensorAccess: true,
    sensorUpdateRate: 16, // 60fps
    ...options
  };

  /**
   * Inicializa a sessão multissensorial
   */
  const initializeSession = useCallback(async (sessionId, sessionConfig = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      // Gerar sessionId se não fornecido
      const finalSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionIdRef.current = finalSessionId;

      // Inicializar o coletor multissensorial usando os métodos corretos
      const initResult = await multisensoryCollector.startMetricsCollection(
        finalSessionId,
        sessionConfig.userId || 'anonymous',
        {
          gameType,
          enableSensorAccess: defaultOptions.enableSensorAccess,
          sensorUpdateRate: defaultOptions.sensorUpdateRate,
          ...sessionConfig
        }
      );

      if (!initResult.success) {
        throw new Error(initResult.error || 'Falha ao inicializar sessão multissensorial');
      }

      // Configurar sessão
      setCurrentSession({
        sessionId: finalSessionId,
        gameType,
        startTime: new Date().toISOString(),
        ...sessionConfig
      });

      setIsInitialized(true);
      
      // Iniciar atualizações automáticas se habilitado
      if (defaultOptions.autoUpdate) {
        startAutoUpdate();
      }
      
      logger.info('🎯 Sessão multissensorial inicializada (DIRECT)', {
        type: 'session_initialized',
        sessionId: finalSessionId,
        gameType,
        autoUpdate: defaultOptions.autoUpdate,
        sensorAccess: defaultOptions.enableSensorAccess,
        timestamp: new Date().toISOString()
      });

      return { success: true, sessionId: finalSessionId };

    } catch (err) {
      setError(err);
      logger.error('❌ Erro ao inicializar sessão multissensorial', {
        type: 'session_init_error',
        error: err.message,
        gameType,
        sessionId
      });
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  }, [gameType, multisensoryCollector, defaultOptions, logger]);

  /**
   * Registra uma interação multissensorial
   */
  const recordInteraction = useCallback(async (action, data = {}) => {
    if (!isInitialized || !sessionIdRef.current) {
      logger.warn('⚠️ Sessão multissensorial não inicializada', {
        type: 'interaction_not_initialized',
        action,
        gameType
      });
      return { success: false, error: 'Session not initialized' };
    }

    try {
      // Obter métricas atuais do multissensorial
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      
      // Normalizar dados da interação do jogo
      const normalizedMetrics = {
        gameId: gameType,
        sessionId: sessionIdRef.current,
        userId: currentSession?.userId || 'anonymous',
        action,
        timestamp: new Date().toISOString(),
        ...data
      };

      // Processar dados multissensoriais se disponíveis
      let result = { success: true, data: normalizedMetrics };
      
      if (currentMetrics && currentMetrics.mobileSensors) {
        const multisensoryData = [currentMetrics];
        const processedResult = await multisensoryCollector.processMultisensoryData(
          normalizedMetrics,
          multisensoryData
        );
        
        if (!processedResult.error) {
          result.data = {
            ...normalizedMetrics,
            multisensoryAnalysis: processedResult
          };
        }
      }

      lastUpdateRef.current = Date.now();

      // Atualizar dados locais se não estiver em modo auto-update
      if (!defaultOptions.autoUpdate) {
        await updateMultisensoryData();
      }

      logger.debug('📊 Interação multissensorial registrada', {
        action,
        sessionId: sessionIdRef.current,
        success: result.success,
        hasMultisensoryData: !!currentMetrics
      });
      
      return result;

    } catch (err) {
      logger.error('❌ Erro ao registrar interação multissensorial', {
        type: 'interaction_error',
        error: err.message,
        action,
        gameType
      });
      setError(err);
      return { success: false, error: err.message };
    }
  }, [isInitialized, gameType, multisensoryCollector, currentSession, defaultOptions, logger]);

  /**
   * Atualiza dados multissensoriais
   */
  const updateMultisensoryData = useCallback(async () => {
    if (!isInitialized || !sessionIdRef.current) return;

    try {
      const currentData = await multisensoryCollector.getCurrentMetrics();
      if (currentData) {
        setMultisensoryData(currentData);
      }
    } catch (err) {
      logger.error('❌ Erro ao atualizar dados multissensoriais', {
        type: 'data_update_error',
        error: err.message,
        gameType
      });
    }
  }, [isInitialized, multisensoryCollector, gameType, logger]);

  /**
   * Inicia atualizações automáticas
   */
  const startAutoUpdate = useCallback(() => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
    }
    
    updateIntervalRef.current = setInterval(() => {
      updateMultisensoryData();
    }, defaultOptions.updateInterval);
  }, [updateMultisensoryData, defaultOptions.updateInterval]);

  /**
   * Para atualizações automáticas
   */
  const stopAutoUpdate = useCallback(() => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
    }
  }, []);

  /**
   * Analisa padrões multissensoriais
   */
  const analyzePatterns = useCallback(async () => {
    if (!isInitialized || !defaultOptions.enablePatternAnalysis || !sessionIdRef.current) {
      return null;
    }
    
    try {
      // Usar getCurrentMetrics para obter padrões atuais
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      if (currentMetrics && currentMetrics.neurodivergencePatterns) {
        return currentMetrics.neurodivergencePatterns;
      }
      return null;
    } catch (err) {
      logger.error('❌ Erro ao analisar padrões', {
        type: 'pattern_analysis_error',
        error: err.message,
        gameType
      });
      return null;
    }
  }, [isInitialized, multisensoryCollector, defaultOptions.enablePatternAnalysis, gameType, logger]);

  /**
   * Finaliza a sessão multissensorial
   */
  const finalizeSession = useCallback(async (sessionData = {}) => {
    if (!isInitialized || !sessionIdRef.current) {
      logger.warn('⚠️ Nenhuma sessão ativa para finalizar', {
        type: 'finalize_not_initialized',
        gameType
      });
      return null;
    }

    try {
      setIsLoading(true);
      stopAutoUpdate();
      
      // Finalizar sessão usando stopMetricsCollection
      const report = await multisensoryCollector.stopMetricsCollection();
      
      if (report.success) {
        setSessionReport(report.report);
      }
      
      // Limpar estado
      setCurrentSession(null);
      setIsInitialized(false);
      setMultisensoryData(null);
      sessionIdRef.current = null;
      
      logger.info('✅ Sessão multissensorial finalizada (DIRECT)', {
        type: 'session_finalized',
        sessionId: sessionIdRef.current,
        gameType,
        reportGenerated: report.success,
        timestamp: new Date().toISOString()
      });

      return report.success ? report.report : null;

    } catch (err) {
      setError(err);
      logger.error('❌ Erro ao finalizar sessão multissensorial', {
        type: 'session_finalize_error',
        error: err.message,
        sessionId: sessionIdRef.current,
        gameType
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, multisensoryCollector, gameType, stopAutoUpdate, logger]);

  /**
   * Obtém recomendações baseadas nos dados atuais
   */
  const getRecommendations = useCallback(async () => {
    if (!multisensoryData || !sessionIdRef.current) return [];
    
    try {
      // Gerar recomendações básicas baseadas nos dados multissensoriais
      const basicRecommendations = [];
      
      // Análise visual baseada nos dados atuais
      if (multisensoryData.mobileSensors) {
        const sensorData = multisensoryData.mobileSensors;
        
        // Análise de movimento
        if (sensorData.accelerometer) {
          const movement = sensorData.accelerometer;
          if (movement.variability > 0.8) {
            basicRecommendations.push('Considere exercícios de estabilização motora');
          }
          if (movement.avgMagnitude < 0.2) {
            basicRecommendations.push('Tente atividades com mais movimentação');
          }
        }
        
        // Análise de orientação
        if (sensorData.gyroscope) {
          const orientation = sensorData.gyroscope;
          if (orientation.stability < 0.5) {
            basicRecommendations.push('Pratique exercícios de orientação espacial');
          }
        }
        
        // Análise tátil
        if (sensorData.touch) {
          const touch = sensorData.touch;
          if (touch.pressure && touch.pressure.average > 0.8) {
            basicRecommendations.push('Experimente toques mais suaves na tela');
          }
          if (touch.frequency < 0.3) {
            basicRecommendations.push('Aumente a frequência de interações');
          }
        }
      }
      
      // Análise de padrões de neurodivergência
      if (multisensoryData.neurodivergencePatterns) {
        const patterns = multisensoryData.neurodivergencePatterns;
        
        if (patterns.stimmingIndicators && patterns.stimmingIndicators.level > 0.7) {
          basicRecommendations.push('Considere pausas regulares para autorregulação');
        }
        
        if (patterns.sensorySeekingLevel > 0.8) {
          basicRecommendations.push('Experimente atividades com estímulos sensoriais variados');
        }
        
        if (patterns.sensoryAvoidanceLevel > 0.7) {
          basicRecommendations.push('Reduza estímulos sensoriais intensos');
        }
      }
      
      return basicRecommendations;
      
    } catch (err) {
      logger.error('❌ Erro ao gerar recomendações', {
        type: 'recommendations_error',
        error: err.message,
        gameType
      });
      
      // Fallback para recomendações muito básicas
      return [
        'Continue praticando para melhorar suas habilidades',
        'Experimente ajustar a velocidade do jogo',
        'Mantenha uma postura confortável durante o jogo'
      ];
    }
  }, [multisensoryData, multisensoryCollector, gameType, logger]);

  /**
   * Obtém dados em tempo real dos sensores
   */
  const getSensorData = useCallback(async () => {
    if (!isInitialized || !sessionIdRef.current) return null;
    
    try {
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      return currentMetrics ? currentMetrics.mobileSensors : null;
    } catch (err) {
      logger.error('❌ Erro ao obter dados dos sensores', {
        type: 'sensor_data_error',
        error: err.message,
        gameType
      });
      return null;
    }
  }, [isInitialized, multisensoryCollector, gameType, logger]);

  /**
   * Obtém métricas de neurodivergência
   */
  const getNeurodivergenceMetrics = useCallback(async () => {
    if (!isInitialized || !sessionIdRef.current) return null;
    
    try {
      const currentMetrics = await multisensoryCollector.getCurrentMetrics();
      return currentMetrics ? currentMetrics.neurodivergencePatterns : null;
    } catch (err) {
      logger.error('❌ Erro ao obter métricas de neurodivergência', {
        type: 'neurodivergence_metrics_error',
        error: err.message,
        gameType
      });
      return null;
    }
  }, [isInitialized, multisensoryCollector, gameType, logger]);

  /**
   * Limpa recursos e para atualizações ao desmontar
   */
  useEffect(() => {
    return () => {
      stopAutoUpdate();
      if (multisensoryCollector && isInitialized) {
        multisensoryCollector.stopMetricsCollection().catch(err => {
          logger.error('❌ Erro ao limpar sessão no cleanup', { error: err.message });
        });
      }
    };
  }, [stopAutoUpdate, multisensoryCollector, isInitialized, logger]);

  // Interface pública do hook
  return {
    // Estado
    isInitialized,
    currentSession,
    multisensoryData,
    sessionReport,
    isLoading,
    error,
    
    // Métodos principais
    initializeSession,
    recordInteraction,
    finalizeSession,
    
    // Análise e recomendações
    analyzePatterns,
    getRecommendations,
    getSensorData,
    getNeurodivergenceMetrics,
    
    // Controle de atualizações
    updateMultisensoryData,
    startAutoUpdate,
    stopAutoUpdate,
    
    // Dados brutos (para depuração)
    collector: defaultOptions.logLevel === 'debug' ? multisensoryCollector : null,
    sessionId: sessionIdRef.current
  };
}

/**
 * Hook simplificado para jogos básicos
 * 
 * REFATORADO v3.1: Versão mais simples do hook para jogos que não precisam 
 * de funcionalidades avançadas. Agora usa MultisensoryMetricsCollector diretamente.
 * 
 * @param {string} gameType - Tipo do jogo
 * @param {Object} collectors - Coletores do jogo (opcional)
 * @returns {Object} Interface simplificada
 */
export function useSimpleMultisensoryIntegration(gameType, collectors) {
  const integration = useMultisensoryIntegration(gameType, collectors, {
    autoUpdate: false,
    enablePatternAnalysis: false,
    logLevel: 'warn',
    enableSensorAccess: true
  });
  
  return {
    isReady: integration.isInitialized,
    startSession: integration.initializeSession,
    recordAction: integration.recordInteraction,
    endSession: integration.finalizeSession,
    getCurrentData: integration.multisensoryData,
    getReport: integration.sessionReport,
    getSensors: integration.getSensorData,
    isLoading: integration.isLoading,
    error: integration.error,
    sessionId: integration.sessionId
  };
}

export default useMultisensoryIntegration;
