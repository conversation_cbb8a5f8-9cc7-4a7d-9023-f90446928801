/**
 * @file BackupExport.module.css
 * @description Estilos para a página de backup e exportação (seguindo padrão About)
 * @version 3.0.0
 */

/* Container principal */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
  position: relative;
}

/* <PERSON><PERSON><PERSON><PERSON> glo<PERSON> */
* {
  box-sizing: border-box;
}

/* Botão Voltar */
.backButton {
  position: fixed;
  top: 2rem;
  left: 2rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 50px;
  padding: 1rem 1.5rem;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* <PERSON> */
.heroBanner {
  text-align: center;
  padding: 6rem 2rem 4rem;
  position: relative;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #fff, #e8f4fd);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

.heroSubtitle {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
  animation: fadeInUp 1s ease-out 0.2s both;
}

/* Badges */
.badgeContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.techBadge {
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.techBadge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.badgePrimary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.8));
}

.badgeGreen {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(22, 163, 74, 0.8));
}

.badgePurple {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.8), rgba(147, 51, 234, 0.8));
}

/* Alert */
.alert {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 500;
  border-left: 4px solid;
  backdrop-filter: blur(10px);
  animation: slideInRight 0.5s ease-out;
}

.alertSuccess {
  background: rgba(34, 197, 94, 0.2);
  color: #dcfce7;
  border-left-color: #22c55e;
}

.alertError {
  background: rgba(239, 68, 68, 0.2);
  color: #fecaca;
  border-left-color: #ef4444;
}

/* Seções */
.section {
  max-width: 1000px;
  margin: 4rem auto;
  padding: 0 2rem;
  animation: fadeInUp 1s ease-out;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.sectionTitle span {
  font-size: 3rem;
  animation: bounce 2s infinite;
}

.sectionContent {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.sectionContent p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  opacity: 0.95;
}

/* Opções de exportação */
.exportOptions {
  margin: 2rem 0;
}

.optionItem {
  margin-bottom: 1rem;
}

.optionLabel {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.optionLabel:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.optionCheckbox {
  width: 1.2rem;
  height: 1.2rem;
  accent-color: #22c55e;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.optionText {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.optionText strong {
  font-size: 1.1rem;
  color: white;
}

.optionText small {
  font-size: 0.9rem;
  opacity: 0.8;
  color: #e5e7eb;
}

/* Botões de ação */
.actionButtons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.actionButton {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.actionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.primaryButton {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
}

.primaryButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #22c55e);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(34, 197, 94, 0.4);
}

.successButton {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.successButton:hover {
  background: linear-gradient(135deg, #1d4ed8, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
}

.secondaryButton {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.secondaryButton:hover {
  background: linear-gradient(135deg, #4b5563, #6b7280);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(107, 114, 128, 0.4);
}

/* Seção de importação */
.importSection {
  margin: 2rem 0;
}

.fileInput {
  display: none;
}

.fileLabel {
  display: inline-block;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fileLabel:hover {
  background: linear-gradient(135deg, #d97706, #f59e0b);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(245, 158, 11, 0.4);
}

/* Barra de progresso */
.progressContainer {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progressLabel {
  margin-bottom: 1rem;
  font-weight: 600;
  color: #e5e7eb;
  text-align: center;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #22c55e, #16a34a);
  border-radius: 4px;
  transition: width 0.3s ease;
  animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Estatísticas do backup */
.statsContainer {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.statsContainer h4 {
  margin-bottom: 1rem;
  color: #e5e7eb;
  font-size: 1.1rem;
  text-align: center;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.statLabel {
  font-size: 0.9rem;
  color: #d1d5db;
}

.statValue {
  font-weight: 600;
  color: #3b82f6;
  font-size: 1rem;
}

/* Preview melhorado */
.previewContainer {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.previewContainer h4 {
  margin-bottom: 1rem;
  color: #e5e7eb;
  font-size: 1.1rem;
}

.previewInfo {
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.previewInfo p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #d1d5db;
}

.previewInfo strong {
  color: #f3f4f6;
}

.previewContent {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #f0f0f0;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  line-height: 1.4;
}

/* Status de Backup */
.backupStatusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.statusItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.statusLabel {
  font-size: 0.9rem;
  color: #d1d5db;
  font-weight: 500;
}

.statusValue {
  font-weight: 600;
  color: #f3f4f6;
  font-size: 0.95rem;
}

.statusValue.enabled {
  color: #22c55e;
}

.statusValue.disabled {
  color: #ef4444;
}

/* Status Card */
.statusCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  margin-top: 2rem;
  border: 2px solid;
}

.statusOnline {
  background: rgba(34, 197, 94, 0.1);
  border-color: #22c55e;
}

.statusOffline {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

.statusIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.statusInfo h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.statusInfo p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

/* Animações */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .heroBanner {
    padding: 4rem 1rem 3rem;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .section {
    margin: 3rem auto;
    padding: 0 1rem;
  }

  .sectionContent {
    padding: 2rem;
  }

  .sectionTitle {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .badgeContainer {
    flex-direction: column;
    align-items: center;
  }

  .actionButtons {
    flex-direction: column;
  }

  .actionButton,
  .fileLabel {
    width: 100%;
    text-align: center;
  }

  .backButton {
    top: 1rem;
    left: 1rem;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .optionLabel {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .statusCard {
    flex-direction: column;
    text-align: center;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .statItem {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .backupStatusGrid {
    grid-template-columns: 1fr;
  }

  .statusItem {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .progressContainer {
    padding: 1rem;
  }

  .previewInfo {
    padding: 0.75rem;
  }

  .previewInfo p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .sectionTitle {
    font-size: 1.5rem;
  }
  
  .sectionContent {
    padding: 1.5rem;
  }
  
  .previewContent {
    font-size: 0.75rem;
  }
}
