<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Betina V3 - Sistema Terapêutico Multissensorial</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .status-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .status-value {
            font-size: 1.1rem;
            color: #28a745;
        }
        
        .api-endpoints {
            margin-top: 30px;
            text-align: left;
        }
        
        .endpoint {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #17a2b8;
        }
        
        .endpoint-method {
            font-weight: bold;
            color: #17a2b8;
            margin-right: 10px;
        }
        
        .endpoint-url {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .health-check {
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 15px;
            color: white;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🧠 Portal Betina V3</div>
        <div class="subtitle">Sistema Terapêutico Multissensorial para Desenvolvimento Cognitivo</div>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-title">Status do Servidor</div>
                <div class="status-value">✅ Online</div>
            </div>
            <div class="status-card">
                <div class="status-title">Porta</div>
                <div class="status-value">4000</div>
            </div>
            <div class="status-card">
                <div class="status-title">Jogos Disponíveis</div>
                <div class="status-value">8 Jogos</div>
            </div>
            <div class="status-card">
                <div class="status-title">APIs</div>
                <div class="status-value">Funcionando</div>
            </div>
        </div>
        
        <div class="health-check">
            <h3>✅ Sistema Funcionando Perfeitamente!</h3>
            <p>Todas as rotas de métricas, coletores e processadores foram corrigidos e estão operacionais.</p>
            <button class="btn" onclick="testAPI()">Testar API</button>
        </div>
        
        <div class="api-endpoints">
            <h3>📋 Principais Endpoints Disponíveis:</h3>
            
            <div class="endpoint">
                <span class="endpoint-method">GET</span>
                <span class="endpoint-url">/api/health</span>
                - Status do sistema
            </div>
            
            <div class="endpoint">
                <span class="endpoint-method">POST</span>
                <span class="endpoint-url">/api/metrics/interactions</span>
                - Registrar interações do usuário
            </div>
            
            <div class="endpoint">
                <span class="endpoint-method">GET</span>
                <span class="endpoint-url">/api/metrics/interactions</span>
                - Listar interações
            </div>
            
            <div class="endpoint">
                <span class="endpoint-method">POST</span>
                <span class="endpoint-url">/api/metrics/multisensory</span>
                - Métricas multissensoriais
            </div>
            
            <div class="endpoint">
                <span class="endpoint-method">GET</span>
                <span class="endpoint-url">/api/metrics/progress</span>
                - Progresso do usuário
            </div>
            
            <div class="endpoint">
                <span class="endpoint-method">GET</span>
                <span class="endpoint-url">/api/dashboard/therapeutic</span>
                - Dashboard terapêutico
            </div>
        </div>
    </div>

    <script>
        async function testAPI() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                alert('API funcionando! Status: ' + (data.success ? 'Sucesso' : 'Erro'));
            } catch (error) {
                alert('Erro ao testar API: ' + error.message);
            }
        }
        
        // Atualizar status a cada 30 segundos
        setInterval(async () => {
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    console.log('✅ Sistema funcionando normalmente');
                }
            } catch (error) {
                console.error('❌ Erro de conexão:', error);
            }
        }, 30000);
    </script>
</body>
</html>
