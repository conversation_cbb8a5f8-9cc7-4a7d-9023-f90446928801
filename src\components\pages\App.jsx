import React, { useState, Suspense, lazy } from 'react'
import './App.css'

// Importando componentes essenciais (sempre carregados)
import Header from '../navigation/Header'
import ActivitiesGridComponent from '../navigation/ActivitiesGrid'
import DonationBanner from '../navigation/DonationBanner'
import ToolsSection from '../navigation/ToolsSection'
import Footer from '../navigation/Footer'

// Lazy loading para componentes pesados
const About = lazy(() => import('./About/About'))
const AdminPanel = lazy(() => import('./AdminPanel/AdminPanel'))
const AccessibilityPage = lazy(() => import('./AccessibilityPage/AccessibilityPage'))
const UserProfiles = lazy(() => import('./UserProfiles/UserProfiles'))
// BackupExport removido - funcionalidade disponível apenas no dashboard premium
const DashboardContainer = lazy(() => import('../dashboard/DashboardContainer'))
const GamePage = lazy(() => import('./GamePage'))

function App() {
  const [currentActivity, setCurrentActivity] = useState('home')
  const [currentPage, setCurrentPage] = useState('home')

  const handleLogoClick = () => {
    setCurrentActivity('home')
    setCurrentPage('home')  }
  
  const handleActivityChange = (activityId) => {
    // Lista de jogos válidos
    const gameIds = [
      'letter-recognition', 'number-counting', 'memory-game', 
      'musical-sequence', 'color-match', 'image-association',
      'creative-painting', 'padroes-visuais', 'quebra-cabeca',
      'letters', 'numbers', 'memory', 'music', 'colors', 'images', 'patterns'
    ]
    
    if (gameIds.includes(activityId)) {
      // Se é um jogo, navegar para a página do jogo
      setCurrentActivity(activityId)
      setCurrentPage('game')
    } else {
      // Se não é um jogo, manter na home
      setCurrentActivity(activityId)
      setCurrentPage('home')
    }
  }

  const handleToolSelect = (toolId) => {
    if (toolId === 'about-info') {
      setCurrentPage('about')
    } else if (toolId === 'admin-panel') {
      setCurrentPage('admin')
    } else if (toolId === 'accessibility-settings') {
      setCurrentPage('accessibility')
    } else if (toolId === 'user-profiles') {
      setCurrentPage('profiles')
    } else if (toolId === 'dashboard-performance') {
      setCurrentPage('dashboards')
    } else {
      setCurrentActivity(toolId)
      setCurrentPage('home')
    }
  }

  const handleBackToHome = () => {
    setCurrentPage('home')
    setCurrentActivity('home')
  }

  // Componente de carregamento
  const LoadingFallback = () => (
    <div className="loading-container" style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '60vh',
      fontSize: '18px',
      color: '#666'
    }}>
      <div>Carregando...</div>
    </div>
  )

  return (
    <div className="app">
      <Header onLogoClick={handleLogoClick} />
      
      <Suspense fallback={<LoadingFallback />}>
        {currentPage === 'about' ? (
          <About onBackToHome={handleBackToHome} />
        ) : currentPage === 'admin' ? (
          <AdminPanel onBack={handleBackToHome} />
        ) : currentPage === 'accessibility' ? (
          <AccessibilityPage onBack={handleBackToHome} />
        ) : currentPage === 'profiles' ? (
          <UserProfiles onBack={handleBackToHome} />
        ) : currentPage === 'dashboards' ? (
          <DashboardContainer onBack={handleBackToHome} />
        ) : currentPage === 'game' ? (
          <GamePage gameId={currentActivity} onBack={handleBackToHome} />
        ) : (
          <main className="main-content">
            <DonationBanner />
            <ActivitiesGridComponent onActivitySelect={handleActivityChange} />
            <ToolsSection onToolSelect={handleToolSelect} />
          </main>
        )}
      </Suspense>

      <Footer 
        currentActivity={currentActivity}
        onActivityChange={handleActivityChange}
      />
    </div>
  )
}

export default App
