/**
 * @file styles.module.css
 * @description Estilos modulares para o Performance Dashboard e componentes relacionados
 * @version 3.0.0
 */

/* MultisensoryMetricsPanel */
.metricsPanelRoot {
  padding: 24px;
  margin-bottom: 24px;
  position: relative;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.metricsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metricsTitle {
  font-size: 20px;
  font-weight: 600;
  color: #334155;
}

.metricsContent {
  min-height: 300px;
}

.metricsEmptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #64748b;
  text-align: center;
}

.metricsChart {
  margin-top: 24px;
  margin-bottom: 24px;
  height: 300px;
}

.metricsLoadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.metricsSpinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
}

.metricsInfoBox {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  margin-top: 16px;
}

.metricsTabs {
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 16px;
  display: flex;
}

.metricsTab {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-weight: 500;
}

.metricsTab.active {
  border-bottom: 2px solid #3b82f6;
  color: #3b82f6;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.metricCard {
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  text-align: center;
}

.metricValue {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3b82f6;
}

.metricLabel {
  font-size: 14px;
  color: #64748b;
  margin-top: 8px;
}

.metricsButton {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

.metricsButton:hover {
  background-color: #2563eb;
}

.metricsButtonSecondary {
  padding: 8px 16px;
  background-color: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

.metricsButtonSecondary:hover {
  background-color: #f1f5f9;
}

.icon {
  font-size: 18px;
  margin-right: 8px;
}

.metricsDivider {
  height: 1px;
  background-color: #e2e8f0;
  margin: 16px 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Container principal do dashboard */
.dashboardBase {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  padding: 2rem;
  z-index: 1;
}

/* Header do dashboard */
.dashboardHeader {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboardTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.dashboardSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
  font-weight: 300;
}

/* Grid de conteúdo */
.dashboardContent {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.dashboardCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.dashboardCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Estatísticas */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gráficos */
.chartsSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chartContainer {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chartTitle {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  color: white;
  text-align: center;
}

.chartWrapper {
  height: 300px;
  position: relative;
}

/* Controles */
.dashboardControls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.controlButton.active {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
}

/* Estados */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 1rem;
}

.errorContainer {
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  margin: 2rem 0;
}

.errorMessage {
  color: #fca5a5;
  margin-bottom: 1rem;
}

.retryButton {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background: #b91c1c;
}

/* Headers de cards */
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.cardTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.cardIcon {
  font-size: 1.5rem;
  opacity: 0.8;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardBase {
    padding: 1rem;
  }
  
  .dashboardTitle {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dashboardContent {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .chartsSection {
    grid-template-columns: 1fr;
  }
  
  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .dashboardControls {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .dashboardBase {
    padding: 0.5rem;
  }
  
  .dashboardHeader {
    padding: 1rem;
  }
  
  .dashboardTitle {
    font-size: 1.5rem;
  }
  
  .dashboardCard {
    padding: 1rem;
  }
}

/* Redução de movimento para acessibilidade */
@media (prefers-reduced-motion: reduce) {
  .dashboardCard,
  .statCard,
  .controlButton {
    transition: none;
  }
  
  .dashboardCard:hover,
  .statCard:hover,
  .controlButton:hover {
    transform: none;
  }
}

/* Alto contraste */
.highContrast .dashboardBase {
  background: #000;
  color: #fff;
}

.highContrast .dashboardCard {
  background: #000;
  border: 2px solid #fff;
}

.highContrast .statValue {
  color: #fff;
}
