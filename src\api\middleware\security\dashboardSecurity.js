import { createLogger } from '../../../utils/logger.js';
import { AppError } from '../error/errorHandler.js';
// import csurf from 'csurf'; // Temporariamente comentado para testes
import rateLimit from 'express-rate-limit';
import { requireRole, requireTherapistOrAdmin } from '../auth/jwt.js';
import sanitize from 'sanitize-html';
import { Counter } from 'prom-client';

// Logger for dashboard security
const dashboardLogger = createLogger('security:dashboard');

// CSRF protection for browser-based dashboard requests - Temporariamente desabilitado
const csrfProtection = (req, res, next) => {
  // TODO: Reativar CSRF quando csurf estiver instalado
  dashboardLogger.warn('CSRF protection temporariamente desabilitado');
  next();
};

// Rate limiter for dashboard endpoints
const dashboardRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // 50 requests per IP
  message: async (req, res) => {
    dashboardLogger.warn('Rate limit exceeded for dashboard', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    return {
      success: false,
      message: 'Too many requests to dashboard. Please try again later.',
      error_code: 'RATE_LIMIT_EXCEEDED',
      retry_after: 900 // 15 minutes
    };
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
  try {
    // Sanitize query parameters
    if (req.query) {
      for (const key of Object.keys(req.query)) {
        if (typeof req.query[key] === 'string') {
          req.query[key] = sanitize(req.query[key], {
            allowedTags: [],
            allowedAttributes: {}
          });
        }
      }
    }

    // Sanitize body for POST/PUT/PATCH requests
    if (req.body && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
      for (const key of Object.keys(req.body)) {
        if (typeof req.body[key] === 'string') {
          req.body[key] = sanitize(req.body[key], {
            allowedTags: [],
            allowedAttributes: {}
          });
        }
      }
    }

    next();
  } catch (error) {
    dashboardLogger.error('Input sanitization failed', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    return next(new AppError('Invalid input detected', 400, 'INVALID_INPUT'));
  }
};

// Session validation middleware
const validateSession = (req, res, next) => {
  if (!req.user || !req.token) {
    dashboardLogger.warn('Invalid session for dashboard access', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    return next(new AppError('Invalid or missing session', 401, 'INVALID_SESSION'));
  }

  // Check if token is still valid (not expired)
  const now = Math.floor(Date.now() / 1000);
  if (req.token.exp < now) {
    dashboardLogger.warn('Expired session token for dashboard', {
      userId: req.user.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    return next(new AppError('Session expired. Please log in again.', 401, 'SESSION_EXPIRED'));
  }

  next();
};

// Dashboard security middleware (combines all protections)
const dashboardSecurity = [
  dashboardRateLimiter,
  requireTherapistOrAdmin, // Restrict to therapist or admin roles
  csrfProtection,
  validateSession,
  sanitizeInput,
  (req, res, next) => {
    // Log successful dashboard access
    dashboardLogger.info('Dashboard access granted', {
      userId: req.user.id,
      username: req.user.username,
      role: req.user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    next();
  }
];

// Admin-only dashboard security (stricter)
const adminDashboardSecurity = [
  dashboardRateLimiter,
  requireRole('admin'), // Restrict to admin role only
  csrfProtection,
  validateSession,
  sanitizeInput,
  (req, res, next) => {
    dashboardLogger.info('Admin dashboard access granted', {
      userId: req.user.id,
      username: req.user.username,
      role: req.user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    next();
  }
];

// Middleware to check for premium dashboard access
const requirePremiumDashboard = (req, res, next) => {
  if (!req.user || !['therapist', 'admin'].includes(req.user.role)) {
    dashboardLogger.warn('Non-premium user attempted dashboard access', {
      userId: req.user?.id || 'unknown',
      role: req.user?.role || 'none',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
      timestamp: new Date().toISOString()
    });
    return next(new AppError('Premium access required for dashboard', 403, 'PREMIUM_ACCESS_REQUIRED'));
  }
  next();
};

// Metrics for dashboard security events
const dashboardAccessFailures = new Counter({
  name: 'dashboard_access_failures_total',
  help: 'Total number of failed dashboard access attempts',
  labelNames: ['error_code', 'path']
});

// Error handling wrapper for dashboard security
const handleDashboardErrors = (err, req, res, next) => {
  dashboardAccessFailures.inc({ error_code: err.code || 'UNKNOWN_ERROR', path: req.originalUrl });
  dashboardLogger.error('Dashboard security error', {
    error: err.message,
    code: err.code,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'unknown',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
  next(err); // Pass to global error handler
};

export {
  dashboardSecurity,
  adminDashboardSecurity,
  requirePremiumDashboard,
  handleDashboardErrors
};