/**
 * @file DatabaseIntegrator.js
 * @description Integrador dos componentes do database da V2 para a V3
 * @version 3.0.1
 * FLUXO: JOGOS → MÉTRICAS → ORQUESTRADOR → DATABASE → DASHBOARDS
 */

/**
 * @class StructuredLogger
 * @description Logger estruturado simplificado para DatabaseIntegrator
 */
class StructuredLogger {
  constructor() {
    this.component = 'DatabaseIntegrator';
  }
  
  info(message, context = {}) {
    console.info(`ℹ️ [${this.component}] ${message}`, context);
  }
  
  warn(message, context = {}) {
    console.warn(`⚠️ [${this.component}] ${message}`, context);
  }
  
  error(message, context = {}) {
    console.error(`❌ [${this.component}] ${message}`, context);
  }
  
  debug(message, context = {}) {
    console.debug(`🔍 [${this.component}] ${message}`, context);
  }
}

/**
 * @class InputValidator
 * @description Validador de entrada para os métodos do DatabaseIntegrator
 */
class InputValidator {
  /**
   * Valida os dados de entrada para saveGameMetrics
   * @param {string} userId - ID do usuário
   * @param {string} gameId - ID do jogo
   * @param {Object} metrics - Objeto de métricas detalhadas
   * @returns {Object} Resultado da validação
   */
  static validateGameMetricsInput(userId, gameId, metrics) {
    const errors = [];
    const warnings = [];

    if (!userId || typeof userId !== 'string') {
      errors.push('userId must be a non-empty string');
    }
    if (!gameId || typeof gameId !== 'string') {
      errors.push('gameId must be a non-empty string');
    }
    if (!metrics || typeof metrics !== 'object') {
      errors.push('metrics must be an object');
    } else {
      if (!metrics.timestamp || !Number.isInteger(metrics.timestamp)) {
        warnings.push('metrics.timestamp should be a valid integer');
      }
      if (metrics.interactions && !Array.isArray(metrics.interactions)) {
        errors.push('metrics.interactions must be an array');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      sanitized: {
        userId: userId ? String(userId).trim() : userId,
        gameId: gameId ? String(gameId).trim() : gameId,
        metrics: metrics ? { ...metrics, timestamp: metrics.timestamp || Date.now() } : metrics
      }
    };
  }

  /**
   * Valida os dados de entrada para saveInteractions
   * @param {number} sessionId - ID da sessão
   * @param {Array<Object>} interactions - Lista de interações
   * @returns {Object} Resultado da validação
   */
  static validateInteractionsInput(sessionId, interactions) {
    const errors = [];
    const warnings = [];

    if (!Number.isInteger(sessionId) || sessionId <= 0) {
      errors.push('sessionId must be a positive integer');
    }
    if (!Array.isArray(interactions) || interactions.length === 0) {
      errors.push('interactions must be a non-empty array');
    } else {
      interactions.forEach((interaction, index) => {
        if (!interaction.type || typeof interaction.type !== 'string') {
          errors.push(`interaction[${index}].type must be a string`);
        }
        if (!Number.isInteger(interaction.timestamp)) {
          warnings.push(`interaction[${index}].timestamp should be a valid integer`);
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      sanitized: {
        sessionId,
        interactions: interactions.map(interaction => ({
          ...interaction,
          type: interaction.type ? String(interaction.type).trim() : interaction.type,
          timestamp: interaction.timestamp || Date.now()
        }))
      }
    };
  }

  /**
   * Valida os dados de entrada para getUserData
   * @param {string} userId - ID do usuário
   * @param {string} dataType - Tipo de dados
   * @returns {Object} Resultado da validação
   */
  static validateUserDataInput(userId, dataType) {
    const errors = [];
    const validDataTypes = ['progress', 'profile', 'predictions'];

    if (!userId || typeof userId !== 'string') {
      errors.push('userId must be a non-empty string');
    }
    if (!dataType || !validDataTypes.includes(dataType)) {
      errors.push(`dataType must be one of: ${validDataTypes.join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      sanitized: {
        userId: userId ? String(userId).trim() : userId,
        dataType
      }
    };
  }
}

/**
 * @class DatabaseIntegrator
 * @description Integra os recursos avançados do banco de dados da V2 com a V3
 */
class DatabaseIntegrator {
  /**
   * @constructor
   * @param {Object} config - Configurações do integrador
   */
  constructor(config = {}) {
    this.config = {
      resilience: {
        enabled: true,
        monitoringEnabled: true,
        retryAttempts: 3,
        retryDelay: 1000,
        timeoutDuration: 5000
      },
      cache: {
        memoryMaxSize: 1000,
        memoryTTL: 300000, // 5 minutes
        redisEnabled: !!config.redisUrl,
        redisUrl: config.redisUrl || null
      },
      metrics: {
        enabled: true,
        detailedLogging: true,
        persistInterval: 60000 // 1 minute
      },
      ...config
    };

    this.logger = new StructuredLogger();
    this.metricsBuffer = new Map();
    this.intervals = new Map();

    this.logger.info('DatabaseIntegrator: Initialized (simplified for frontend)', {
      config: {
        resilience: this.config.resilience.enabled,
        cache: this.config.cache.redisEnabled ? 'redis' : 'memory',
        metrics: this.config.metrics.enabled
      }
    });
  }

  /**
   * Interface unificada para persistência de métricas detalhadas
   * @param {string} userId - ID do usuário
   * @param {string} gameId - ID do jogo
   * @param {Object} metrics - Objeto de métricas detalhadas
   * @returns {Promise<Object>} Confirmação da persistência
   */
  async saveGameMetrics(userId, gameId, metrics) {
    try {
      const validation = InputValidator.validateGameMetricsInput(userId, gameId, metrics);
      if (!validation.valid) {
        throw new Error(`Invalid input: ${validation.errors.join(', ')}`);
      }

      const { userId: sanitizedUserId, gameId: sanitizedGameId, metrics: sanitizedMetrics } = validation.sanitized;

      // Simulação para o frontend - na versão real conectaria com o backend
      const result = {
        sessionId: Date.now(),
        userId: sanitizedUserId,
        gameId: sanitizedGameId,
        timestamp: Date.now(),
        status: 'success'
      };

      this.logger.info('Game metrics saved successfully (frontend)', {
        userId: sanitizedUserId,
        gameId: sanitizedGameId,
        sessionId: result.sessionId,
        metricsCount: Object.keys(sanitizedMetrics).length
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to save game metrics', {
        userId,
        gameId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Salva as interações detalhadas no banco de dados
   * @param {number} sessionId - ID da sessão
   * @param {Array<Object>} interactions - Lista de interações
   * @returns {Promise<Object>} Confirmação da persistência
   */
  async saveInteractions(sessionId, interactions) {
    try {
      const validation = InputValidator.validateInteractionsInput(sessionId, interactions);
      if (!validation.valid) {
        throw new Error(`Invalid interactions input: ${validation.errors.join(', ')}`);
      }

      const { sessionId: sanitizedSessionId, interactions: sanitizedInteractions } = validation.sanitized;

      // Simulação para o frontend
      const result = {
        sessionId: sanitizedSessionId,
        interactionCount: sanitizedInteractions.length,
        timestamp: Date.now(),
        status: 'success'
      };

      this.logger.info('Interactions saved successfully (frontend)', {
        sessionId: sanitizedSessionId,
        interactionCount: sanitizedInteractions.length
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to save interactions', {
        sessionId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Interface unificada para recuperação de dados com cache e resiliência
   * @param {string} userId - ID do usuário
   * @param {string} dataType - Tipo de dados ('progress', 'profile', 'predictions')
   * @returns {Promise<Object>} Dados solicitados
   */
  async getUserData(userId, dataType) {
    try {
      const validation = InputValidator.validateUserDataInput(userId, dataType);
      if (!validation.valid) {
        throw new Error(`Invalid input: ${validation.errors.join(', ')}`);
      }

      const { userId: sanitizedUserId, dataType: sanitizedDataType } = validation.sanitized;

      // Simulação para o frontend
      const result = {
        userId: sanitizedUserId,
        dataType: sanitizedDataType,
        data: {},
        timestamp: Date.now(),
        status: 'success'
      };

      this.logger.info('User data retrieved successfully (frontend)', {
        userId: sanitizedUserId,
        dataType: sanitizedDataType
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to retrieve user data', {
        userId,
        dataType,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Obtém status completo do sistema
   * @returns {Object} Status de todos os componentes
   */
  getStatus() {
    try {
      const status = {
        database: {
          connected: true,
          isConnected: true,
          type: 'frontend-simulation',
          status: 'operational'
        },
        resilience: { status: 'operational' },
        sessionManager: { status: 'operational' },
        metricsEngine: { status: 'operational' },
        systemOrchestrator: { status: 'operational' },
        metricsBuffer: {
          size: this.metricsBuffer.size,
          status: 'operational'
        },
        timestamp: new Date().toISOString()
      };

      return status;
    } catch (error) {
      this.logger.error('Error retrieving system status', { error: error.message, stack: error.stack });
      return {
        database: { connected: false, isConnected: false, type: 'frontend-simulation', status: 'failed' },
        resilience: { status: 'failed' },
        sessionManager: { status: 'failed' },
        metricsEngine: { status: 'failed' },
        systemOrchestrator: { status: 'failed' },
        metricsBuffer: { size: 0, status: 'failed' },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Obtém status do sistema integrador
   * @returns {Object} Status do sistema
   */
  getSystemStatus() {
    return this.getStatus();
  }

  /**
   * Salva métricas multissensoriais no banco de dados
   * @param {string} userId - ID do usuário
   * @param {string} sessionId - ID da sessão
   * @param {Object} multisensoryData - Dados multissensoriais coletados
   * @returns {Promise<Object>} Resultado da operação
   */
  async saveMultisensoryMetrics(userId, sessionId, multisensoryData) {
    try {
      // Validar entrada
      if (!userId || !sessionId || !multisensoryData) {
        throw new Error('userId, sessionId and multisensoryData são obrigatórios');
      }

      // Estruturar dados para o banco
      const metrics = {
        session_id: sessionId,
        user_id: userId,
        multisensory_data: multisensoryData.multisensoryData,
        sensor_readings: multisensoryData.sensorReadings || [],
        neurodivergence_patterns: multisensoryData.neurodivergencePatterns || {},
        analysis_results: multisensoryData.analysisResults || {},
        session_duration: multisensoryData.sessionDuration || 0,
        device_context: multisensoryData.deviceContext || {},
        timestamp: new Date().toISOString(),
        game_type: 'multisensory_session'
      };

      // Usar saveGameMetrics existente para persistir
      const result = await this.saveGameMetrics(userId, 'multisensory_metrics', metrics);

      this.logger.info('✅ Métricas multissensoriais salvas com sucesso', {
        userId,
        sessionId,
        sensorDataPoints: multisensoryData.sensorReadings?.length || 0,
        analysisSummary: multisensoryData.analysisResults?.summary || 'N/A'
      });

      return {
        success: true,
        sessionId: result.sessionId,
        savedAt: new Date().toISOString(),
        dataPoints: multisensoryData.sensorReadings?.length || 0
      };

    } catch (error) {
      this.logger.error('❌ Erro ao salvar métricas multissensoriais', {
        userId,
        sessionId,
        error: error.message,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        savedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Limpa recursos e finaliza intervalos
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info('Initiating DatabaseIntegrator cleanup...');

      // Parar intervalos
      for (const [intervalName, intervalId] of this.intervals.entries()) {
        clearInterval(intervalId);
        this.logger.info(`Interval stopped: ${intervalName}`);
      }
      this.intervals.clear();

      this.logger.info('✅ DatabaseIntegrator cleanup completed');
    } catch (error) {
      this.logger.error('Error during DatabaseIntegrator cleanup', { error: error.message, stack: error.stack });
      throw error;
    }
  }
}

export default DatabaseIntegrator;
export { InputValidator, DatabaseIntegrator };
