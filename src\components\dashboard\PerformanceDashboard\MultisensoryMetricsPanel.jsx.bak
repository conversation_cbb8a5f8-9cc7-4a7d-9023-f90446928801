/**
 * @file MultisensoryMetricsPanel.jsx
 * @description Painel para visualização de métricas multissensoriais no PerformanceDashboard
 */

import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Chart } from 'react-chartjs-2';
import styles from './styles.module.css';

/**
 * Ícones personalizados para substituir os do Material UI
 */
const InfoIcon = () => <span className={styles.icon}>ℹ️</span>;
const BarChartIcon = () => <span className={styles.icon}>📊</span>;
const DevicesIcon = () => <span className={styles.icon}>📱</span>;
const TouchAppIcon = () => <span className={styles.icon}>👆</span>;

/**
 * Componente para exibição de métricas multissensoriais
 */
const MultisensoryMetricsPanel = ({ userId, gameType, sessionData = null }) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [metricsData, setMetricsData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Carregar dados multissensoriais
  const loadMultisensoryData = useCallback(async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      setError(null);

      // Se temos sessionData diretamente, usamos
      if (sessionData && sessionData.sensorMetrics) {
        setMetricsData(sessionData);
        return;
      }
      
      // Caso contrário, tentamos buscar do localStorage para teste/desenvolvimento
      const cachedData = localStorage.getItem(`multisensory_${userId}_${gameType || 'all'}`);
      if (cachedData) {
        setMetricsData(JSON.parse(cachedData));
        return;
      }
      
      // Implementação de um mock para demonstração
      // Em produção, aqui seria uma chamada API real
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulação de latência
      
      const mockData = generateMockData(userId, gameType);
      setMetricsData(mockData);
      
      // Cache para desenvolvimento
      localStorage.setItem(`multisensory_${userId}_${gameType || 'all'}`, JSON.stringify(mockData));
    } catch (err) {
      console.error('Erro ao carregar dados multissensoriais', err);
      setError('Não foi possível carregar os dados multissensoriais');
    } finally {
      setIsLoading(false);
    }
  }, [userId, gameType, sessionData]);
  
  // Carregar dados ao inicializar o componente
  useEffect(() => {
    loadMultisensoryData();
  }, [loadMultisensoryData]);
  
  // Mudança de tab
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };
  
  // Exibir estado vazio quando não há dados
  if (!metricsData && !isLoading) {
    return (
      <div className={styles.metricsPanelRoot}>
        <div className={styles.metricsHeader}>
          <h3 className={styles.metricsTitle}>Métricas Multissensoriais</h3>
        </div>
        <div className={styles.metricsDivider}></div>
        <div className={styles.metricsEmptyState}>
          <DevicesIcon />
          <p style={{ marginBottom: '12px', fontSize: '16px' }}>
            Não há dados multissensoriais disponíveis
          </p>
          <p style={{ marginBottom: '16px', fontSize: '14px', color: '#64748b' }}>
            Os dados são coletados automaticamente durante os jogos com multisensores habilitados
          </p>
          <button 
            className={styles.metricsButton}
            onClick={loadMultisensoryData}
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={styles.metricsPanelRoot}>
      {isLoading && (
        <div className={styles.metricsLoadingOverlay}>
          <div className={styles.metricsSpinner}></div>
        </div>
      )}
      
      <div className={styles.metricsHeader}>
        <h3 className={styles.metricsTitle}>Métricas Multissensoriais</h3>
        <div>
          <button 
            className={styles.metricsButtonSecondary}
            onClick={() => alert('As métricas multissensoriais permitem analisar como o usuário interage fisicamente com os jogos, fornecendo insights sobre padrões motores, estabilidade e consistência.')}
          >
            <InfoIcon /> Sobre estas métricas
          </button>
        </div>
      </div>
      
      <div className={styles.metricsDivider}></div>
      
      <div className={styles.metricsTabs}>
        <div 
          className={`${styles.metricsTab} ${currentTab === 0 ? styles.active : ''}`} 
          onClick={() => handleTabChange(null, 0)}
        >
          <BarChartIcon /> Visão Geral
        </div>
        <div 
          className={`${styles.metricsTab} ${currentTab === 1 ? styles.active : ''}`} 
          onClick={() => handleTabChange(null, 1)}
        >
          <TouchAppIcon /> Interação
        </div>
        <div 
          className={`${styles.metricsTab} ${currentTab === 2 ? styles.active : ''}`} 
          onClick={() => handleTabChange(null, 2)}
        >
          <DevicesIcon /> Sensores
        </div>
      </div>
      
      <div className={styles.tabContent}>
        {currentTab === 0 && (
          <div>
            <h4 style={{ marginBottom: '16px', fontSize: '18px' }}>
              Resumo de Métricas Multissensoriais
            </h4>
            
            <div className={styles.metricsGrid}>
              <MetricCard 
                title="Sessões" 
                value={metricsData?.summary?.sessions || 0} 
                suffix="total" 
              />
              <MetricCard 
                title="Pontos de Dados" 
                value={metricsData?.summary?.dataPoints || 0} 
                suffix="coletados" 
              />
              <MetricCard 
                title="Sensores Disponíveis" 
                value={metricsData?.summary?.sensorsAvailable || 0} 
                suffix="de 4" 
              />
              <MetricCard 
                title="Estabilidade" 
                value={metricsData?.deviceHandling?.stability || 0} 
                suffix="%" 
                color="#3b82f6"
              />
            </div>
            
            {metricsData?.aggregatedMetrics && (
              <div className={styles.metricsChart}>
                <p style={{ fontSize: '14px', color: '#64748b', marginBottom: '8px' }}>
                  Métricas de Interação Agregadas
                </p>
                <Chart 
                  type="radar" 
                  data={prepareRadarData(metricsData.aggregatedMetrics)}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    elements: {
                      line: {
                        borderWidth: 3
                      }
                    },
                    plugins: {
                      legend: {
                        position: 'top',
                      }
                    }
                  }}
                />
              </div>
            )}
          </div>
        )}
        
        {currentTab === 1 && (
          <div>
            <h4 style={{ marginBottom: '16px', fontSize: '18px' }}>
              Métricas de Interação
            </h4>
            
            <div className={styles.metricsGrid}>
              <MetricCard 
                title="Precisão de Toque" 
                value={metricsData?.touchInteractions?.accuracy || 0} 
                suffix="%" 
              />
              <MetricCard 
                title="Tempo de Reação" 
                value={metricsData?.touchInteractions?.reactionTime || 0} 
                suffix="ms" 
              />
              <MetricCard 
                title="Consistência" 
                value={metricsData?.touchInteractions?.consistency || 0} 
                suffix="%" 
              />
              <MetricCard 
                title="Controle Fino" 
                value={metricsData?.touchInteractions?.fineControl || 0} 
                suffix="pts" 
              />
            </div>
            
            <div className={styles.metricsInfoBox}>
              <p style={{ fontSize: '14px', marginBottom: '8px' }}>
                <InfoIcon /> As métricas de interação são baseadas na análise de padrões de toque, pressão e tempo de resposta durante as atividades.
              </p>
              <p style={{ fontSize: '14px', color: '#64748b' }}>
                Uma maior consistência e precisão de toque podem indicar melhor coordenação motora fina.
              </p>
            </div>
            
            {metricsData?.touchInteractions?.history && (
              <div className={styles.metricsChart}>
                <p style={{ fontSize: '14px', color: '#64748b', marginBottom: '8px' }}>
                  Evolução da Precisão de Toque
                </p>
                <Chart 
                  type="line"
                  data={prepareLineData(metricsData.touchInteractions.history, 'Precisão (%)')}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top',
                      }
                    }
                  }}
                />
              </div>
            )}
          </div>
        )}
        
        {currentTab === 2 && (
          <div>
            <h4 style={{ marginBottom: '16px', fontSize: '18px' }}>
              Métricas de Sensores
            </h4>
            
            <div className={styles.metricsGrid}>
              <MetricCard 
                title="Acelerômetro" 
                value={metricsData?.deviceSensors?.accelerometer ? 'Ativo' : 'Inativo'} 
                color={metricsData?.deviceSensors?.accelerometer ? '#22c55e' : '#64748b'} 
              />
              <MetricCard 
                title="Giroscópio" 
                value={metricsData?.deviceSensors?.gyroscope ? 'Ativo' : 'Inativo'} 
                color={metricsData?.deviceSensors?.gyroscope ? '#22c55e' : '#64748b'} 
              />
              <MetricCard 
                title="Orientação" 
                value={metricsData?.deviceSensors?.orientation ? 'Ativo' : 'Inativo'} 
                color={metricsData?.deviceSensors?.orientation ? '#22c55e' : '#64748b'} 
              />
              <MetricCard 
                title="Touch Avançado" 
                value={metricsData?.deviceSensors?.advancedTouch ? 'Ativo' : 'Inativo'} 
                color={metricsData?.deviceSensors?.advancedTouch ? '#22c55e' : '#64748b'} 
              />
            </div>
            
            <div className={styles.metricsInfoBox}>
              <p style={{ fontSize: '14px', marginBottom: '8px' }}>
                <InfoIcon /> Os sensores disponíveis dependem do dispositivo utilizado. Nem todos os dispositivos possuem todos os sensores.
              </p>
              <p style={{ fontSize: '14px', color: '#64748b' }}>
                Para uma experiência multissensorial completa, recomenda-se o uso de um dispositivo com acelerômetro e giroscópio.
              </p>
            </div>
            
            {metricsData?.deviceHandling?.steadiness && (
              <div className={styles.metricsChart}>
                <p style={{ fontSize: '14px', color: '#64748b', marginBottom: '8px' }}>
                  Estabilidade de Manuseio do Dispositivo
                </p>
                <Chart 
                  type="bar"
                  data={prepareBarData(metricsData.deviceHandling.steadiness, 'Estabilidade')}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top',
                      }
                    }
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Componente para cartão de métrica individual
const MetricCard = ({ title, value, suffix = '', color = 'inherit' }) => {  
  return (
    <div className={styles.metricCard}>
      <div className={styles.metricLabel}>
        {title}
      </div>
      <div className={styles.metricValue} style={{ color: color !== 'inherit' ? color : undefined }}>
        {value} {suffix && <span style={{ fontSize: '0.8rem' }}>{suffix}</span>}
      </div>
    </div>
  );
};

// Preparar dados para o gráfico radar
const prepareRadarData = (metrics) => {
  return {
    labels: [
      'Precisão', 
      'Tempo de Reação', 
      'Controle', 
      'Consistência', 
      'Coordenação'
    ],
    datasets: [
      {
        label: 'Usuário',
        data: [
          metrics.accuracy,
          metrics.reactionTime,
          metrics.control,
          metrics.consistency,
          metrics.coordination
        ],
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
      },
      {
        label: 'Média',
        data: [
          metrics.avgAccuracy,
          metrics.avgReactionTime,
          metrics.avgControl,
          metrics.avgConsistency,
          metrics.avgCoordination
        ],
        backgroundColor: 'rgba(148, 163, 184, 0.2)',
        borderColor: 'rgba(148, 163, 184, 1)',
        borderWidth: 2,
      }
    ]
  };
};

// Preparar dados para gráfico de linha
const prepareLineData = (history, label) => {
  return {
    labels: history.map(item => item.date),
    datasets: [
      {
        label,
        data: history.map(item => item.value),
        fill: false,
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        tension: 0.4
      }
    ]
  };
};

// Preparar dados para gráfico de barras
const prepareBarData = (data, label) => {
  return {
    labels: Object.keys(data),
    datasets: [
      {
        label,
        data: Object.values(data),
        backgroundColor: [
          'rgba(59, 130, 246, 0.7)',
          'rgba(34, 197, 94, 0.7)',
          'rgba(239, 68, 68, 0.7)',
          'rgba(168, 85, 247, 0.7)',
        ],
        borderWidth: 1
      }
    ]
  };
};

// Função para gerar dados mock para desenvolvimento
const generateMockData = (userId, gameType) => {
  // Geração de dados aleatórios para desenvolvimento
  const randomPercent = () => Math.floor(Math.random() * 100);
  const randomInRange = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
  
  const getDates = (count) => {
    const dates = [];
    const today = new Date();
    for (let i = count - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i * 3);
      dates.push(date.toLocaleDateString('pt-BR'));
    }
    return dates;
  };
  
  const dates = getDates(7);
  
  return {
    userId,
    gameType: gameType || 'all',
    summary: {
      sessions: randomInRange(5, 30),
      dataPoints: randomInRange(1000, 10000),
      sensorsAvailable: randomInRange(2, 4),
      lastUpdated: new Date().toISOString()
    },
    aggregatedMetrics: {
      accuracy: randomPercent(),
      reactionTime: randomPercent(),
      control: randomPercent(),
      consistency: randomPercent(),
      coordination: randomPercent(),
      avgAccuracy: 75,
      avgReactionTime: 70,
      avgControl: 65,
      avgConsistency: 60,
      avgCoordination: 68
    },
    touchInteractions: {
      accuracy: randomPercent(),
      reactionTime: randomInRange(200, 800),
      consistency: randomPercent(),
      fineControl: randomInRange(50, 100),
      history: dates.map(date => ({
        date,
        value: randomPercent()
      }))
    },
    deviceHandling: {
      stability: randomPercent(),
      steadiness: {
        'Muito Estável': randomInRange(10, 40),
        'Estável': randomInRange(20, 50),
        'Pouco Estável': randomInRange(5, 20),
        'Instável': randomInRange(0, 10)
      }
    },
    deviceSensors: {
      accelerometer: Math.random() > 0.2,
      gyroscope: Math.random() > 0.3,
      orientation: Math.random() > 0.1,
      advancedTouch: Math.random() > 0.4
    }
  };
};

MultisensoryMetricsPanel.propTypes = {
  userId: PropTypes.string,
  gameType: PropTypes.string,
  sessionData: PropTypes.object
};

MetricCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  suffix: PropTypes.string,
  color: PropTypes.string
};

export default MultisensoryMetricsPanel;
