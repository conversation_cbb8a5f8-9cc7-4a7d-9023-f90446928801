/**
 * @file ToolsSection.jsx
 * @description Seção de ferramentas e configurações do Portal Betina
 * @version 3.0.0
 */

import React from 'react'
import styles from './ToolsSection.module.css'

function ToolsSection({ onToolSelect }) {  const handleToolClick = (toolId, toolTitle, toolType = 'info') => {
    if (onToolSelect) {
      onToolSelect(toolId)
    }
    // Removido: notificação desnecessária
  }
  const tools = [
    {
      id: 'dashboard-performance',
      title: 'Dashboards do Sistema',
      description: 'Performance, IA, Neuropedagógico, Multissensorial e Sistema Integrado',
      icon: '📊',
      badge: 'Premium',
      color: 'premium',
      type: 'premium'
    },
    {
      id: 'user-profiles',
      title: 'Perfis de Usuário',
      description: 'Gerencie diferentes perfis para toda a família',
      icon: '👤',
      badge: 'Perfis',
      color: 'purple',
      type: 'info'
    },
    {
      id: 'backup-export',
      title: 'Backup e Exportação Premium',
      description: 'Acesse recursos avançados de backup no dashboard premium',
      icon: '💾',
      badge: 'Premium',
      color: 'gold',
      type: 'premium'
    },
    {
      id: 'admin-panel',
      title: 'Painel Administrativo',
      description: 'Configurações avançadas e gerenciamento do sistema',
      icon: '🔐',
      badge: 'Admin',
      color: 'red',
      type: 'warning'
    },
    {
      id: 'about-info',
      title: 'Sobre o Portal',
      description: 'Informações sobre o projeto, versão e créditos',
      icon: 'ℹ️',
      badge: 'Info',
      color: 'cyan',
      type: 'info'
    },
    {
      id: 'accessibility-settings',
      title: 'Configurações de Acessibilidade',
      description: 'Alto contraste, tamanho da fonte e outras opções de acessibilidade',
      icon: '♿',
      badge: 'Acessibilidade',
      color: 'special',
      type: 'special'
    }
  ]

  return (
    <section className={styles.container}>
      <h2 className={styles.title}>
        ⚙️ Ferramentas e Configurações
      </h2>

      <div className={styles.grid}>
        {tools.map((tool, index) => (
          <button
            key={tool.id}
            className={`${styles.card} ${styles[tool.color] || styles.blue}`}
            onClick={() => handleToolClick(tool.id, tool.title, tool.type)}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={styles.cardHeader}>
              <div className={styles.icon}>{tool.icon}</div>
              <span className={`${styles.badge} ${styles[`badge${tool.color.charAt(0).toUpperCase() + tool.color.slice(1)}`] || styles.badgeBlue}`}>
                {tool.badge}
              </span>
            </div>
            
            <h3 className={styles.cardTitle}>{tool.title}</h3>
            <p className={styles.cardDescription}>{tool.description}</p>
          </button>
        ))}
      </div>
    </section>
  )
}

export default ToolsSection
