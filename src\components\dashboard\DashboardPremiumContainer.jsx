/**
 * @file DashboardPremiumContainer.jsx
 * @description Container principal para dashboards premium com sistema de autenticação
 * @version 3.0.0
 */

import React, { useState, useEffect, Suspense } from 'react'
import { usePremium } from '../../context/PremiumContext.jsx'
import { useUser } from '../../contexts/UserContext.jsx'
import PremiumGate from './Premium/PremiumGate.jsx'
import LoadingSpinner from '../common/LoadingSpinner/LoadingSpinner.jsx'

// Lazy loading dos dashboards para otimizar performance
const PerformanceDashboard = React.lazy(() => 
  import('./PerformanceDashboard/PerformanceDashboard.jsx').catch(() => ({ 
    default: () => <div>Erro ao carregar Performance Dashboard</div> 
  }))
)
const NeuropedagogicalDashboard = React.lazy(() => 
  import('./NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx').catch(() => ({ 
    default: () => <div>Erro ao carregar Neuropedagogical Dashboard</div> 
  }))
)
const AdvancedAIReport = React.lazy(() => 
  import('./AdvancedAIReport/AdvancedAIReport.jsx').catch(() => ({ 
    default: () => <div>Erro ao carregar AI Report</div> 
  }))
)

// Configuração dos dashboards premium
const DASHBOARD_CONFIG = {
  performance: {
    id: 'performance',
    title: 'Dashboard de Performance',
    description: 'Análise detalhada de desempenho e métricas',
    icon: '📊',
    premium: true,
    component: PerformanceDashboard
  },
  neuropedagogical: {
    id: 'neuropedagogical',
    title: 'Análise Neuropedagógica',
    description: 'Insights educacionais especializados',
    icon: '🎓',
    premium: true,
    component: NeuropedagogicalDashboard
  },
  aiReports: {
    id: 'aiReports',
    title: 'Relatórios com IA',
    description: 'Relatórios inteligentes e preditivos',
    icon: '🤖',
    premium: true,
    component: AdvancedAIReport
  }
}

const DashboardPremiumContainer = ({ 
  initialDashboard = 'performance',
  className = '',
  ...props 
}) => {
  const { isPremium, canAccessDashboard, hasFeatureAccess } = usePremium()
  const { userId, userDetails } = useUser()
  const [activeDashboard, setActiveDashboard] = useState(initialDashboard)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Verificar se o dashboard solicitado existe
  useEffect(() => {
    if (!DASHBOARD_CONFIG[initialDashboard]) {
      setActiveDashboard('performance')
    }
  }, [initialDashboard])

  // Função para verificar acesso ao dashboard
  const canAccessSpecificDashboard = (dashboardId) => {
    const dashboard = DASHBOARD_CONFIG[dashboardId]
    if (!dashboard) return false
    
    if (dashboard.premium) {
      return isPremium && canAccessDashboard()
    }
    
    return true
  }

  // Renderizar navegação entre dashboards
  const renderNavigation = () => {
    return (
      <div className="dashboard-navigation">
        <div className="dashboard-tabs">
          {Object.values(DASHBOARD_CONFIG).map(dashboard => (
            <button
              key={dashboard.id}
              className={`dashboard-tab ${activeDashboard === dashboard.id ? 'active' : ''}`}
              onClick={() => setActiveDashboard(dashboard.id)}
              disabled={!canAccessSpecificDashboard(dashboard.id)}
            >
              <span className="dashboard-tab-icon">{dashboard.icon}</span>
              <span className="dashboard-tab-title">{dashboard.title}</span>
              {dashboard.premium && !isPremium && (
                <span className="premium-badge">💎</span>
              )}
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Renderizar dashboard ativo
  const renderActiveDashboard = () => {
    const dashboard = DASHBOARD_CONFIG[activeDashboard]
    if (!dashboard) return null

    // Verificar acesso
    if (!canAccessSpecificDashboard(activeDashboard)) {
      return (
        <PremiumGate
          feature="dashboard"
          title={`${dashboard.title} - Premium`}
          description={`${dashboard.description}. Este dashboard avançado está disponível apenas para usuários premium.`}
          showUpgrade={true}
        />
      )
    }

    const DashboardComponent = dashboard.component

    // Props comuns para todos os dashboards
    const commonProps = {
      userId,
      userDetails,
      isPremiumUser: isPremium,
      onError: setError,
      onLoading: setLoading,
      ...props
    }

    return (
      <Suspense fallback={<LoadingSpinner message="Carregando dashboard..." />}>
        <div className="dashboard-content">
          <div className="dashboard-header">
            <h2 className="dashboard-title">
              <span className="dashboard-icon">{dashboard.icon}</span>
              {dashboard.title}
            </h2>
            <p className="dashboard-description">{dashboard.description}</p>
          </div>
          
          <div className="dashboard-body">
            <DashboardComponent {...commonProps} />
          </div>
        </div>
      </Suspense>
    )
  }

  // Renderizar error state
  if (error) {
    return (
      <div className="dashboard-error">
        <div className="dashboard-error-content">
          <h3>⚠️ Erro no Dashboard</h3>
          <p>{error}</p>
          <button 
            className="dashboard-retry-button"
            onClick={() => {
              setError(null)
              setLoading(false)
            }}
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`dashboard-premium-container ${className}`}>
      {renderNavigation()}
      
      <div className="dashboard-main">
        {loading && <LoadingSpinner message="Carregando dados..." />}
        {renderActiveDashboard()}
      </div>

      <style>{`
        .dashboard-premium-container {
          display: flex;
          flex-direction: column;
          height: 100%;
          background: #000000;
          color: white;
          border-radius: var(--radius-large);
          overflow: hidden;
        }

        .dashboard-navigation {
          background: rgba(255, 255, 255, 0.05);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          padding: var(--space-md);
        }

        .dashboard-tabs {
          display: flex;
          gap: var(--space-xs);
          overflow-x: auto;
          padding-bottom: var(--space-xs);
        }

        .dashboard-tab {
          display: flex;
          align-items: center;
          gap: var(--space-xs);
          padding: var(--space-sm) var(--space-md);
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: var(--radius-medium);
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: all 0.3s ease;
          white-space: nowrap;
          min-width: max-content;
          position: relative;
        }

        .dashboard-tab:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: white;
        }

        .dashboard-tab.active {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          color: white;
          font-weight: 600;
        }

        .dashboard-tab:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .dashboard-tab-icon {
          font-size: 1.2rem;
        }

        .dashboard-tab-title {
          font-size: var(--font-size-sm);
        }

        .premium-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          font-size: 0.8rem;
        }

        .dashboard-main {
          flex: 1;
          overflow: auto;
          padding: var(--space-lg);
        }

        .dashboard-content {
          height: 100%;
        }

        .dashboard-header {
          margin-bottom: var(--space-lg);
        }

        .dashboard-title {
          display: flex;
          align-items: center;
          gap: var(--space-sm);
          margin: 0 0 var(--space-xs) 0;
          font-size: var(--font-size-xl);
          color: white;
        }

        .dashboard-icon {
          font-size: 1.5rem;
        }

        .dashboard-description {
          color: rgba(255, 255, 255, 0.7);
          margin: 0;
          font-size: var(--font-size-sm);
        }

        .dashboard-body {
          height: calc(100% - 100px);
          overflow: auto;
        }

        .dashboard-error {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          padding: var(--space-lg);
        }

        .dashboard-error-content {
          text-align: center;
          background: rgba(255, 0, 0, 0.1);
          border: 1px solid rgba(255, 0, 0, 0.3);
          border-radius: var(--radius-medium);
          padding: var(--space-lg);
          max-width: 400px;
        }

        .dashboard-retry-button {
          background: var(--primary-blue);
          color: white;
          border: none;
          border-radius: var(--radius-medium);
          padding: var(--space-sm) var(--space-md);
          cursor: pointer;
          margin-top: var(--space-md);
          transition: all 0.3s ease;
        }

        .dashboard-retry-button:hover {
          background: var(--primary-blue-dark);
        }

        @media (max-width: 768px) {
          .dashboard-tabs {
            flex-direction: column;
          }
          
          .dashboard-tab {
            width: 100%;
            justify-content: center;
          }
          
          .dashboard-main {
            padding: var(--space-md);
          }
        }
      `}</style>
    </div>
  )
}

export default DashboardPremiumContainer
