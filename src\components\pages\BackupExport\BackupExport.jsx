/**
 * @file BackupExport.jsx
 * @description Página de backup e exportação de dados - Versão Melhorada
 * @version 3.1.0
 */

import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import styles from './BackupExport.module.css'

function BackupExport({ onBack, userId = 'user_demo', isDbConnected = false, userDetails = null, onImportComplete = () => {} }) {
  const [backupData, setBackupData] = useState(null)
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [alert, setAlert] = useState(null)
  const [importFile, setImportFile] = useState(null)
  const [importPreview, setImportPreview] = useState(null)
  const [backupStats, setBackupStats] = useState(null)
  const [exportProgress, setExportProgress] = useState(0)
  const [backupStatus, setBackupStatus] = useState(null)
  const [exportOptions, setExportOptions] = useState({
    userProfiles: true,
    gameProgress: true,
    accessibilitySettings: true,
    preferences: true,
    gameMetrics: true,
    sessionData: true,
  })

  // Buscar status de backup ao carregar o componente
  useEffect(() => {
    const fetchBackupStatus = async () => {
      if (isDbConnected && userId) {
        try {
          const response = await fetch(`/api/backup/status/${userId}`)
          if (response.ok) {
            const data = await response.json()
            setBackupStatus(data.data)
          }
        } catch (error) {
          console.warn('Erro ao buscar status de backup:', error)
        }
      }
    }

    fetchBackupStatus()
  }, [isDbConnected, userId])

  // Limpar alertas após um tempo
  useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => {
        setAlert(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [alert])

  // Função para gerar o backup dos dados (melhorada)
  const generateBackup = async () => {
    if (!userId) {
      setAlert({
        type: 'error',
        message: 'Erro: Não foi possível identificar o usuário.',
      })
      return
    }

    setIsExporting(true)
    setExportProgress(0)

    try {
      let backupData = {
        version: '3.1.0',
        exportDate: new Date().toISOString(),
        userId: userId,
        userDetails: userDetails,
        data: {},
        metadata: {
          source: isDbConnected ? 'database_and_local' : 'local_only',
          totalItems: 0,
          categories: []
        }
      }

      let totalItems = 0
      const categories = []

      // Progresso: 10%
      setExportProgress(10)

      // Backup de perfis de usuário
      if (exportOptions.userProfiles) {
        const profiles = localStorage.getItem('betina_profiles')
        if (profiles) {
          try {
            backupData.data.userProfiles = JSON.parse(profiles)
            totalItems += Array.isArray(backupData.data.userProfiles) ? backupData.data.userProfiles.length : 1
            categories.push('userProfiles')
          } catch (e) {
            backupData.data.userProfiles = profiles
          }
        }
      }

      // Progresso: 25%
      setExportProgress(25)

      // Backup de progresso dos jogos (melhorado)
      if (exportOptions.gameProgress) {
        const gameData = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key.startsWith('betina_') && (key.includes('_history') || key.includes('_progress') || key.includes('_scores'))) {
            try {
              gameData[key] = JSON.parse(localStorage.getItem(key))
              totalItems++
            } catch (e) {
              gameData[key] = localStorage.getItem(key)
            }
          }
        }
        backupData.data.gameProgress = gameData
        if (Object.keys(gameData).length > 0) categories.push('gameProgress')
      }

      // Progresso: 50%
      setExportProgress(50)

      // Backup de métricas de jogos
      if (exportOptions.gameMetrics) {
        const metricsData = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key.startsWith('betina_') && key.includes('_metrics')) {
            try {
              metricsData[key] = JSON.parse(localStorage.getItem(key))
              totalItems++
            } catch (e) {
              metricsData[key] = localStorage.getItem(key)
            }
          }
        }
        backupData.data.gameMetrics = metricsData
        if (Object.keys(metricsData).length > 0) categories.push('gameMetrics')
      }

      // Progresso: 75%
      setExportProgress(75)

      // Backup de configurações de acessibilidade
      if (exportOptions.accessibilitySettings) {
        const accessSettings = localStorage.getItem('betina_accessibility_settings')
        if (accessSettings) {
          try {
            backupData.data.accessibilitySettings = JSON.parse(accessSettings)
            totalItems++
            categories.push('accessibilitySettings')
          } catch (e) {
            backupData.data.accessibilitySettings = accessSettings
          }
        }
      }

      // Backup de preferências
      if (exportOptions.preferences) {
        const preferences = localStorage.getItem('betina_user_preferences')
        if (preferences) {
          try {
            backupData.data.preferences = JSON.parse(preferences)
            totalItems++
            categories.push('preferences')
          } catch (e) {
            backupData.data.preferences = preferences
          }
        }
      }

      // Backup de dados de sessão
      if (exportOptions.sessionData) {
        const sessionData = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key.startsWith('betina_') && key.includes('_session')) {
            try {
              sessionData[key] = JSON.parse(localStorage.getItem(key))
              totalItems++
            } catch (e) {
              sessionData[key] = localStorage.getItem(key)
            }
          }
        }
        backupData.data.sessionData = sessionData
        if (Object.keys(sessionData).length > 0) categories.push('sessionData')
      }

      // Progresso: 90%
      setExportProgress(90)

      // Se conectado ao banco, tentar buscar dados adicionais
      if (isDbConnected) {
        try {
          const response = await fetch('/api/backup/user-data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId,
              options: exportOptions
            })
          })

          if (response.ok) {
            const serverData = await response.json()
            backupData.data.serverData = serverData.data
            totalItems += serverData.totalItems || 0
            categories.push('serverData')
          }
        } catch (error) {
          console.warn('Não foi possível obter dados do servidor:', error)
        }
      }

      // Finalizar metadados
      backupData.metadata.totalItems = totalItems
      backupData.metadata.categories = categories

      // Progresso: 100%
      setExportProgress(100)

      // Calcular estatísticas do backup
      const stats = {
        totalSize: JSON.stringify(backupData).length,
        totalItems: totalItems,
        categories: categories.length,
        compressionRatio: 0.85, // Estimativa
        estimatedDownloadTime: Math.ceil(JSON.stringify(backupData).length / 1024 / 100) // segundos estimados
      }

      setBackupData(backupData)
      setBackupStats(stats)
      setAlert({
        type: 'success',
        message: `Backup gerado com sucesso! ${totalItems} itens em ${categories.length} categorias.`,
      })
    } catch (error) {
      console.error('Erro ao gerar backup:', error)
      setAlert({
        type: 'error',
        message: `Erro ao gerar backup: ${error.message}`,
      })
    } finally {
      setIsExporting(false)
      setExportProgress(0)
    }
  }

  // Função para fazer download do backup (melhorada)
  const downloadBackup = () => {
    if (!backupData) return

    const dataStr = JSON.stringify(backupData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
    const filename = `betina-backup-${userId}-${timestamp}.json`

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = filename
    link.click()

    URL.revokeObjectURL(link.href)

    // Log do download para estatísticas
    console.log(`Backup baixado: ${filename}, Tamanho: ${(dataBlob.size / 1024).toFixed(2)} KB`)

    setAlert({
      type: 'success',
      message: `Backup baixado com sucesso! Arquivo: ${filename}`,
    })
  }

  // Função para lidar com o upload do arquivo
  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (!file) return

    setImportFile(file)

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result)
        setImportPreview(jsonData)
      } catch (error) {
        setAlert({
          type: 'error',
          message: 'Arquivo JSON inválido. Verifique o formato.',
        })
        setImportFile(null)
      }
    }
    reader.readAsText(file)
  }

  // Função para importar os dados (melhorada)
  const importBackup = async () => {
    if (!importPreview) return

    setIsImporting(true)

    try {
      if (!importPreview.version) {
        throw new Error('Formato de backup inválido ou não reconhecido')
      }

      // Verificar compatibilidade de versão
      const backupVersion = parseFloat(importPreview.version)
      if (backupVersion < 3.0) {
        throw new Error('Versão do backup não suportada. Versão mínima: 3.0.0')
      }

      let importResults = {
        local: { success: 0, errors: 0 },
        server: { success: 0, errors: 0 }
      }

      // Importar dados locais
      if (importPreview.data.gameProgress) {
        try {
          Object.entries(importPreview.data.gameProgress).forEach(([key, value]) => {
            localStorage.setItem(key, JSON.stringify(value))
          })
          importResults.local.success++
        } catch (error) {
          importResults.local.errors++
          console.warn('Erro ao importar progresso dos jogos:', error)
        }
      }

      if (importPreview.data.gameMetrics) {
        try {
          Object.entries(importPreview.data.gameMetrics).forEach(([key, value]) => {
            localStorage.setItem(key, JSON.stringify(value))
          })
          importResults.local.success++
        } catch (error) {
          importResults.local.errors++
          console.warn('Erro ao importar métricas dos jogos:', error)
        }
      }

      if (importPreview.data.accessibilitySettings) {
        try {
          localStorage.setItem(
            'betina_accessibility_settings',
            JSON.stringify(importPreview.data.accessibilitySettings)
          )
          importResults.local.success++
        } catch (error) {
          importResults.local.errors++
          console.warn('Erro ao importar configurações de acessibilidade:', error)
        }
      }

      if (importPreview.data.preferences) {
        try {
          localStorage.setItem(
            'betina_user_preferences',
            JSON.stringify(importPreview.data.preferences)
          )
          importResults.local.success++
        } catch (error) {
          importResults.local.errors++
          console.warn('Erro ao importar preferências:', error)
        }
      }

      if (importPreview.data.sessionData) {
        try {
          Object.entries(importPreview.data.sessionData).forEach(([key, value]) => {
            localStorage.setItem(key, JSON.stringify(value))
          })
          importResults.local.success++
        } catch (error) {
          importResults.local.errors++
          console.warn('Erro ao importar dados de sessão:', error)
        }
      }

      // Se conectado ao banco, tentar importar dados do servidor
      if (isDbConnected && importPreview.data.serverData) {
        try {
          const response = await fetch('/api/backup/import', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId,
              backupData: importPreview,
              options: exportOptions
            })
          })

          if (response.ok) {
            const serverResult = await response.json()
            importResults.server.success = serverResult.results?.imported?.length || 0
            importResults.server.errors = serverResult.results?.errors?.length || 0
          } else {
            importResults.server.errors++
            console.warn('Erro ao importar dados do servidor')
          }
        } catch (error) {
          importResults.server.errors++
          console.warn('Erro na comunicação com o servidor:', error)
        }
      }

      const totalSuccess = importResults.local.success + importResults.server.success
      const totalErrors = importResults.local.errors + importResults.server.errors

      if (totalSuccess > 0) {
        setAlert({
          type: 'success',
          message: `Dados importados com sucesso! ${totalSuccess} categorias importadas${totalErrors > 0 ? `, ${totalErrors} com erro` : ''}. Pode ser necessário recarregar a página.`,
        })
      } else {
        throw new Error('Nenhum dado foi importado com sucesso')
      }

      // Limpar os campos após importação bem-sucedida
      setImportFile(null)
      setImportPreview(null)
      onImportComplete()

    } catch (error) {
      console.error('Erro ao importar backup:', error)
      setAlert({
        type: 'error',
        message: `Erro ao importar backup: ${error.message}`,
      })
    } finally {
      setIsImporting(false)
    }
  }

  // Função para alterar opções de exportação
  const handleExportOptionChange = (option) => {
    setExportOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }))
  }

  return (
    <div className={styles.container}>
      {/* Botão Voltar */}
      {onBack && (
        <button 
          className={styles.backButton}
          onClick={onBack}
          aria-label="Voltar para a página inicial"
        >
          ← Voltar ao Menu Principal
        </button>
      )}
      
      {/* Hero Banner */}
      <div className={styles.heroBanner}>
        <div className={styles.heroContent}>
          <h1 className={styles.heroTitle}>💾 Backup e Exportação</h1>
          <p className={styles.heroSubtitle}>
            Mantenha seus dados seguros e transfira suas informações entre dispositivos
          </p>
          <div className={styles.badgeContainer}>
            <span className={`${styles.techBadge} ${styles.badgePrimary}`}>
              🔒 Dados Seguros
            </span>
            <span className={`${styles.techBadge} ${styles.badgeGreen}`}>
              📱 Multi-dispositivo
            </span>
            <span className={`${styles.techBadge} ${styles.badgePurple}`}>
              ⚡ Rápido e Fácil
            </span>
          </div>
        </div>
      </div>

      {/* Alert */}
      {alert && (
        <div className={`${styles.alert} ${styles[`alert${alert.type.charAt(0).toUpperCase() + alert.type.slice(1)}`]}`} role="alert">
          {alert.message}
        </div>
      )}

      {/* Seção: Exportar Dados */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>
          <span>📤</span>
          Exportar Dados
        </h2>
        <div className={styles.sectionContent}>
          <p>
            <strong>Crie um backup completo</strong> dos seus dados para guardar com segurança 
            ou transferir para outro dispositivo. Selecione quais informações deseja incluir:
          </p>
          
          <div className={styles.exportOptions}>
            <div className={styles.optionItem}>
              <label className={styles.optionLabel}>
                <input
                  type="checkbox"
                  checked={exportOptions.userProfiles}
                  onChange={() => handleExportOptionChange('userProfiles')}
                  className={styles.optionCheckbox}
                />
                <span className={styles.optionText}>
                  <strong>👤 Perfis de usuário</strong>
                  <small>Informações dos perfis criados e configurações pessoais</small>
                </span>
              </label>
            </div>

            <div className={styles.optionItem}>
              <label className={styles.optionLabel}>
                <input
                  type="checkbox"
                  checked={exportOptions.gameProgress}
                  onChange={() => handleExportOptionChange('gameProgress')}
                  className={styles.optionCheckbox}
                />
                <span className={styles.optionText}>
                  <strong>🎮 Progresso nos jogos</strong>
                  <small>Histórico, pontuações e progressão em todos os jogos</small>
                </span>
              </label>
            </div>

            <div className={styles.optionItem}>
              <label className={styles.optionLabel}>
                <input
                  type="checkbox"
                  checked={exportOptions.gameMetrics}
                  onChange={() => handleExportOptionChange('gameMetrics')}
                  className={styles.optionCheckbox}
                />
                <span className={styles.optionText}>
                  <strong>📊 Métricas de jogos</strong>
                  <small>Dados detalhados de performance e análises</small>
                </span>
              </label>
            </div>

            <div className={styles.optionItem}>
              <label className={styles.optionLabel}>
                <input
                  type="checkbox"
                  checked={exportOptions.sessionData}
                  onChange={() => handleExportOptionChange('sessionData')}
                  className={styles.optionCheckbox}
                />
                <span className={styles.optionText}>
                  <strong>🕐 Dados de sessão</strong>
                  <small>Informações de sessões terapêuticas e atividades</small>
                </span>
              </label>
            </div>

            <div className={styles.optionItem}>
              <label className={styles.optionLabel}>
                <input
                  type="checkbox"
                  checked={exportOptions.accessibilitySettings}
                  onChange={() => handleExportOptionChange('accessibilitySettings')}
                  className={styles.optionCheckbox}
                />
                <span className={styles.optionText}>
                  <strong>♿ Configurações de acessibilidade</strong>
                  <small>Preferências de alto contraste, fonte, navegação, etc.</small>
                </span>
              </label>
            </div>

            <div className={styles.optionItem}>
              <label className={styles.optionLabel}>
                <input
                  type="checkbox"
                  checked={exportOptions.preferences}
                  onChange={() => handleExportOptionChange('preferences')}
                  className={styles.optionCheckbox}
                />
                <span className={styles.optionText}>
                  <strong>⚙️ Preferências gerais</strong>
                  <small>Configurações personalizadas do sistema e interface</small>
                </span>
              </label>
            </div>
          </div>

          {/* Barra de progresso */}
          {isExporting && (
            <div className={styles.progressContainer}>
              <div className={styles.progressLabel}>
                Gerando backup... {exportProgress}%
              </div>
              <div className={styles.progressBar}>
                <div
                  className={styles.progressFill}
                  style={{ width: `${exportProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className={styles.actionButtons}>
            <button
              onClick={generateBackup}
              disabled={isExporting || !userId}
              className={`${styles.actionButton} ${styles.primaryButton}`}
            >
              {isExporting ? '🔄 Gerando...' : '📦 Gerar Backup'}
            </button>

            {backupData && (
              <button
                onClick={downloadBackup}
                className={`${styles.actionButton} ${styles.successButton}`}
              >
                💾 Baixar Backup
              </button>
            )}
          </div>

          {/* Estatísticas do backup */}
          {backupStats && (
            <div className={styles.statsContainer}>
              <h4>📊 Estatísticas do Backup:</h4>
              <div className={styles.statsGrid}>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Total de itens:</span>
                  <span className={styles.statValue}>{backupStats.totalItems}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Categorias:</span>
                  <span className={styles.statValue}>{backupStats.categories}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Tamanho:</span>
                  <span className={styles.statValue}>{(backupStats.totalSize / 1024).toFixed(2)} KB</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Tempo estimado:</span>
                  <span className={styles.statValue}>{backupStats.estimatedDownloadTime}s</span>
                </div>
              </div>
            </div>
          )}

          {backupData && (
            <div className={styles.previewContainer}>
              <h4>📋 Preview do Backup:</h4>
              <div className={styles.previewInfo}>
                <p><strong>Versão:</strong> {backupData.version}</p>
                <p><strong>Data:</strong> {new Date(backupData.exportDate).toLocaleString('pt-BR')}</p>
                <p><strong>Usuário:</strong> {backupData.userId}</p>
                <p><strong>Fonte:</strong> {backupData.metadata?.source || 'local'}</p>
                <p><strong>Categorias:</strong> {backupData.metadata?.categories?.join(', ') || 'N/A'}</p>
              </div>
              <pre className={styles.previewContent}>
                <code>{JSON.stringify(backupData, null, 2).substring(0, 800)}...</code>
              </pre>
            </div>
          )}
        </div>
      </section>

      {/* Seção: Importar Dados */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>
          <span>📥</span>
          Importar Dados
        </h2>
        <div className={styles.sectionContent}>
          <p>
            <strong>Restaure seus dados</strong> a partir de um backup anterior. 
            Selecione o arquivo de backup (.json) que você deseja importar:
          </p>

          <div className={styles.importSection}>
            <input
              type="file"
              accept=".json"
              onChange={handleFileUpload}
              className={styles.fileInput}
              id="import-file"
            />
            <label htmlFor="import-file" className={styles.fileLabel}>
              📁 Escolher arquivo de backup (.json)
            </label>
          </div>

          {importPreview && (
            <>
              <div className={styles.previewContainer}>
                <h4>👀 Preview dos dados para importar:</h4>
                <pre className={styles.previewContent}>
                  <code>{JSON.stringify(importPreview, null, 2).substring(0, 500)}...</code>
                </pre>
              </div>

              <div className={styles.actionButtons}>
                <button
                  onClick={importBackup}
                  disabled={isImporting}
                  className={`${styles.actionButton} ${styles.primaryButton}`}
                >
                  {isImporting ? '🔄 Importando...' : '📥 Importar Dados'}
                </button>

                <button
                  onClick={() => {
                    setImportFile(null)
                    setImportPreview(null)
                  }}
                  className={`${styles.actionButton} ${styles.secondaryButton}`}
                >
                  ❌ Cancelar
                </button>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Seção: Status de Backup */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>
          <span>📊</span>
          Status de Backup
        </h2>
        <div className={styles.sectionContent}>
          {backupStatus ? (
            <div className={styles.backupStatusGrid}>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>Último backup:</span>
                <span className={styles.statusValue}>
                  {new Date(backupStatus.lastBackup).toLocaleString('pt-BR')}
                </span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>Total de backups:</span>
                <span className={styles.statusValue}>{backupStatus.totalBackups}</span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>Tamanho total:</span>
                <span className={styles.statusValue}>
                  {(backupStatus.totalDataSize / 1024 / 1024).toFixed(2)} MB
                </span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.statusLabel}>Backup automático:</span>
                <span className={`${styles.statusValue} ${backupStatus.autoBackupEnabled ? styles.enabled : styles.disabled}`}>
                  {backupStatus.autoBackupEnabled ? 'Ativado' : 'Desativado'}
                </span>
              </div>
            </div>
          ) : (
            <p>
              {isDbConnected
                ? 'Carregando informações de backup...'
                : 'Conecte-se à internet para ver o status dos backups na nuvem.'}
            </p>
          )}
        </div>
      </section>

      {/* Seção: Status de Sincronização */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>
          <span>🔄</span>
          Sincronização Automática
        </h2>
        <div className={styles.sectionContent}>
          <p>
            {isDbConnected
              ? 'Seus dados são sincronizados automaticamente quando você está conectado à internet.'
              : 'No modo offline, seus dados são armazenados apenas localmente neste dispositivo.'}
          </p>

          <div className={`${styles.statusCard} ${isDbConnected ? styles.statusOnline : styles.statusOffline}`}>
            <div className={styles.statusIcon}>
              {isDbConnected ? '🟢' : '🔴'}
            </div>
            <div className={styles.statusInfo}>
              <h4>Status da Sincronização: {isDbConnected ? 'Ativada' : 'Desativada'}</h4>
              <p>
                {isDbConnected
                  ? 'Seus dados estão sendo salvos automaticamente na nuvem.'
                  : 'Seus dados estão sendo salvos apenas localmente. Considere fazer backup regularmente.'}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

BackupExport.propTypes = {
  onBack: PropTypes.func,
  userId: PropTypes.string,
  isDbConnected: PropTypes.bool,
  userDetails: PropTypes.object,
  onImportComplete: PropTypes.func
}

export default BackupExport
