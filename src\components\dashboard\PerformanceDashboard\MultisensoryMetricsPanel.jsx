/**
 * @file MultisensoryMetricsPanel.jsx
 * @description Painel para visualização de métricas multissensoriais no PerformanceDashboard
 * @version 1.1.0 - Enhanced with Tailwind CSS, TypeScript, and improved error handling
 */

import React, { useState, useEffect, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import { Chart as ChartJS, Radar, Line, Bar, Title, Tooltip, Legend, Filler } from 'chart.js';
import { Chart } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(Radar, Line, Bar, Title, Tooltip, Legend, Filler);

// Type definitions
interface MetricData {
  userId?: string;
  gameType?: string;
  summary?: {
    sessions: number;
    dataPoints: number;
    sensorsAvailable: number;
    lastUpdated?: string;
  };
  aggregatedMetrics?: {
    accuracy: number;
    reactionTime: number;
    control: number;
    consistency: number;
    coordination: number;
    avgAccuracy: number;
    avgReactionTime: number;
    avgControl: number;
    avgConsistency: number;
    avgCoordination: number;
  };
  touchInteractions?: {
    accuracy: number;
    reactionTime: number;
    consistency: number;
    fineControl: number;
    history?: Array<{ date: string; value: number }>;
  };
  deviceHandling?: {
    stability: number;
    steadiness: Record<string, number>;
  };
  deviceSensors?: {
    accelerometer: boolean;
    gyroscope: boolean;
    orientation: boolean;
    advancedTouch: boolean;
  };
}

interface MultisensoryMetricsPanelProps {
  userId: string;
  gameType?: string;
  sessionData?: MetricData | null;
}

interface MetricCardProps {
  title: string;
  value: number | string;
  suffix?: string;
  color?: string;
}

/**
 * Componente para exibição de métricas multissensoriais
 */
const MultisensoryMetricsPanel: React.FC<MultisensoryMetricsPanelProps> = memo(
  ({ userId, gameType, sessionData }) => {
    const [currentTab, setCurrentTab] = useState<number>(0);
    const [metricsData, setMetricsData] = useState<MetricData | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState<number>(0);

    const MAX_RETRIES = 3;

    // Carregar dados multissensoriais
    const loadMultisensoryData = useCallback(async () => {
      if (!userId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Use sessionData if provided
        if (sessionData?.summary) {
          setMetricsData(sessionData);
          return;
        }

        // Check localStorage for cached data
        const cacheKey = `multisensory_${userId}_${gameType || 'all'}`;
        const cachedData = localStorage.getItem(cacheKey);
        if (cachedData) {
          setMetricsData(JSON.parse(cachedData));
          return;
        }

        // Simulate API call with mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        const mockData = generateMockData(userId, gameType);
        setMetricsData(mockData);
        localStorage.setItem(cacheKey, JSON.stringify(mockData));
      } catch (err) {
        console.error('Erro ao carregar dados multissensoriais:', err);
        if (retryCount < MAX_RETRIES) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => loadMultisensoryData(), 2000);
        } else {
          setError('Falha ao carregar dados após várias tentativas');
        }
      } finally {
        setIsLoading(false);
      }
    }, [userId, gameType, sessionData, retryCount]);

    useEffect(() => {
      const controller = new AbortController();
      loadMultisensoryData();

      return () => controller.abort();
    }, [loadMultisensoryData]);

    const handleTabChange = useCallback((newValue: number) => {
      setCurrentTab(newValue);
    }, []);

    if (!metricsData && !isLoading) {
      return (
        <div className="bg-white p-6 rounded-lg shadow-md" role="region" aria-label="Painel de métricas multissensoriais">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-800">Métricas Multissensoriais</h3>
          </div>
          <div className="border-t border-gray-200 my-4"></div>
          <div className="text-center py-8">
            <span className="text-2xl" role="img" aria-label="Ícone de dispositivo">📱</span>
            <p className="mt-3 text-lg text-gray-700">Não há dados multissensoriais disponíveis</p>
            <p className="mt-2 text-sm text-gray-500">
              Os dados são coletados automaticamente durante os jogos com multisensores habilitados
            </p>
            <button
              onClick={loadMultisensoryData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              disabled={isLoading}
              aria-label="Tentar carregar dados novamente"
            >
              Tentar novamente
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white p-6 rounded-lg shadow-md relative" role="region" aria-label="Painel de métricas multissensoriais">
        {isLoading && (
          <div className="absolute inset-0 bg-gray-100 bg-opacity-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
          </div>
        )}

        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-800">Métricas Multissensoriais</h3>
          <button
            onClick={() => alert('As métricas multissensoriais permitem analisar como o usuário interage fisicamente com os jogos, fornecendo insights sobre padrões motores, estabilidade e consistência.')}
            className="flex items-center gap-2 text-blue-500 hover:text-blue-600"
            aria-label="Informações sobre métricas"
          >
            <span role="img" aria-label="Ícone de informação">ℹ️</span>
            Sobre estas métricas
          </button>
        </div>

        <div className="border-t border-gray-200 my-4"></div>

        <div className="flex gap-2 mb-6" role="tablist">
          {['Visão Geral', 'Interação', 'Sensores'].map((tab, index) => (
            <button
              key={tab}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded ${currentTab === index ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
              onClick={() => handleTabChange(index)}
              role="tab"
              aria-selected={currentTab === index}
              aria-controls={`panel-${index}`}
            >
              <span className="mr-2" role="img" aria-label={`Ícone de ${tab}`}>
                {index === 0 ? '📊' : index === 1 ? '👆' : '📱'}
              </span>
              {tab}
            </button>
          ))}
        </div>

        <div id={`panel-${currentTab}`} role="tabpanel">
          {currentTab === 0 && (
            <div>
              <h4 className="text-lg font-medium text-gray-800 mb-4">Resumo de Métricas Multissensoriais</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard title="Sessões" value={metricsData?.summary?.sessions || 0} suffix="total" />
                <MetricCard title="Pontos de Dados" value={metricsData?.summary?.dataPoints || 0} suffix="coletados" />
                <MetricCard title="Sensores Disponíveis" value={metricsData?.summary?.sensorsAvailable || 0} suffix="de 4" />
                <MetricCard title="Estabilidade" value={metricsData?.deviceHandling?.stability || 0} suffix="%" color="#3b82f6" />
              </div>
              {metricsData?.aggregatedMetrics && (
                <div className="mt-6">
                  <p className="text-sm text-gray-500 mb-2">Métricas de Interação Agregadas</p>
                  <div className="h-64">
                    <Chart
                      type="radar"
                      data={prepareRadarData(metricsData.aggregatedMetrics)}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { stepSize: 20 }
                          }
                        },
                        plugins: {
                          legend: { position: 'top' },
                          tooltip: { mode: 'index' }
                        }
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {currentTab === 1 && (
            <div>
              <h4 className="text-lg font-medium text-gray-800 mb-4">Métricas de Interação</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard title="Precisão de Toque" value={metricsData?.touchInteractions?.accuracy || 0} suffix="%" />
                <MetricCard title="Tempo de Reação" value={metricsData?.touchInteractions?.reactionTime || 0} suffix="ms" />
                <MetricCard title="Consistência" value={metricsData?.touchInteractions?.consistency || 0} suffix="%" />
                <MetricCard title="Controle Fino"調べ

System: Here's the improved and corrected version of the MultisensoryMetricsPanel.jsx file with enhanced features, TypeScript support, Tailwind CSS, better error handling, and proper Chart.js integration:

<xaiArtifact artifact_id="5236b48f-bab9-408b-ab82-9ec429577c58" artifact_version_id="4749262f-611b-48ae-9936-9660fa5bfe76" title="MultisensoryMetricsPanel.jsx" contentType="text/jsx">
/**
 * @file MultisensoryMetricsPanel.jsx
 * @description Painel para visualização de métricas multissensoriais no PerformanceDashboard
 * @version 1.1.0 - Enhanced with Tailwind CSS, TypeScript, and improved error handling
 */

import React, { useState, useEffect, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import { Chart as ChartJS, Radar, Line, Bar, Title, Tooltip, Legend, Filler } from 'chart.js';
import { Chart } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(Radar, Line, Bar, Title, Tooltip, Legend, Filler);

// Type definitions
interface MetricData {
  userId?: string;
  gameType?: string;
  summary?: {
    sessions: number;
    dataPoints: number;
    sensorsAvailable: number;
    lastUpdated?: string;
  };
  aggregatedMetrics?: {
    accuracy: number;
    reactionTime: number;
    control: number;
    consistency: number;
    coordination: number;
    avgAccuracy: number;
    avgReactionTime: number;
    avgControl: number;
    avgConsistency: number;
    avgCoordination: number;
        
  };
  touchInteractions?: {
    accuracy: number;
    reactionTime: number;
    consistency: number;
    fineControl: number;
    history?: Array<{ date: string; value: number }>;
  };
  deviceHandling?: {
    stability: number;
    steadiness: Record<string, number>;
  };
  deviceSensors?: {
    accelerometer: boolean;
    gyroscope: boolean;
    orientation: boolean;
    advancedTouch: boolean;
  };
}

interface MultisensoryMetricsPanelProps {
  userId: string;
  gameType?: string;
  sessionData?: MetricData | null;
}

interface MetricCardProps {
  title: string;
  value: number | string;
  suffix?: string;
  color?: string;
}

/**
 * Componente para exibição de métricas multissensoriais
 */
const MultisensoryMetricsPanel: React.FC<MultisensoryMetricsPanelProps> = memo(
  ({ userId, gameType, sessionData }) => {
    const [currentTab, setCurrentTab] = useState<number>(0);
    const [metricsData, setMetricsData] = useState<MetricData | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState<number>(0);

    const MAX_RETRIES = 3;

    // Carregar dados multissensoriais
    const loadMultisensoryData = useCallback(async () => {
      if (!userId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Use sessionData if provided
        if (sessionData?.summary) {
          setMetricsData(sessionData);
          return;
        }

        // Check localStorage for cached data
        const cacheKey = `multisensory_${userId}_${gameType || 'all'}`;
        const cachedData = localStorage.getItem(cacheKey);
        if (cachedData) {
          setMetricsData(JSON.parse(cachedData));
          return;
        }

        // Simulate API call with mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        const mockData = generateMockData(userId, gameType);
        setMetricsData(mockData);
        localStorage.setItem(cacheKey, JSON.stringify(mockData));
      } catch (err) {
        console.error('Erro ao carregar dados multissensoriais:', err);
        if (retryCount < MAX_RETRIES) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => loadMultisensoryData(), 2000);
        } else {
          setError('Falha ao carregar dados após várias tentativas');
        }
      } finally {
        setIsLoading(false);
      }
    }, [userId, gameType, sessionData, retryCount]);

    useEffect(() => {
      const controller = new AbortController();
      loadMultisensoryData();

      return () => controller.abort();
    }, [loadMultisensoryData]);

    const handleTabChange = useCallback((newValue: number) => {
      setCurrentTab(newValue);
    }, []);

    if (!metricsData && !isLoading) {
      return (
        <div className="bg-white p-6 rounded-lg shadow-md" role="region" aria-label="Painel de métricas multissensoriais">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-800">Métricas Multissensoriais</h3>
          </div>
          <div className="border-t border-gray-200 my-4"></div>
          <div className="text-center py-8">
            <span className="text-2xl" role="img" aria-label="Ícone de dispositivo">📱</span>
            <p className="mt-3 text-lg text-gray-700">Não há dados multissensoriais disponíveis</p>
            <p className="mt-2 text-sm text-gray-500">
              Os dados são coletados automaticamente durante os jogos com multisensores habilitados
            </p>
            <button
              onClick={loadMultisensoryData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              disabled={isLoading}
              aria-label="Tentar carregar dados novamente"
            >
              Tentar novamente
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white p-6 rounded-lg shadow-md relative" role="region" aria-label="Painel de métricas multissensoriais">
        {isLoading && (
          <div className="absolute inset-0 bg-gray-100 bg-opacity-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
          </div>
        )}

        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-800">Métricas Multissensoriais</h3>
          <button
            onClick={() => alert('As métricas multissensoriais permitem analisar como o usuário interage fisicamente com os jogos, fornecendo insights sobre padrões motores, estabilidade e consistência.')}
            className="flex items-center gap-2 text-blue-500 hover:text-blue-600"
            aria-label="Informações sobre métricas"
          >
            <span role="img" aria-label="Ícone de informação">ℹ️</span>
            Sobre estas métricas
          </button>
        </div>

        <div className="border-t border-gray-200 my-4"></div>

        <div className="flex gap-2 mb-6" role="tablist">
          {['Visão Geral', 'Interação', 'Sensores'].map((tab, index) => (
            <button
              key={tab}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded ${currentTab === index ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
              onClick={() => handleTabChange(index)}
              role="tab"
              aria-selected={currentTab === index}
              aria-controls={`panel-${index}`}
            >
              <span className="mr-2" role="img" aria-label={`Ícone de ${tab}`}>
                {index === 0 ? '📊' : index === 1 ? '👆' : '📱'}
              </span>
              {tab}
            </button>
          ))}
        </div>

        <div id={`panel-${currentTab}`} role="tabpanel">
          {currentTab === 0 && (
            <div>
              <h4 className="text-lg font-medium text-gray-800 mb-4">Resumo de Métricas Multissensoriais</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard title="Sessões" value={metricsData?.summary?.sessions || 0} suffix="total" />
                <MetricCard title="Pontos de Dados" value={metricsData?.summary?.dataPoints || 0} suffix="coletados" />
                <MetricCard title="Sensores Disponíveis" value={metricsData?.summary?.sensorsAvailable || 0} suffix="de 4" />
                <MetricCard title="Estabilidade" value={metricsData?.deviceHandling?.stability || 0} suffix="%" color="#3b82f6" />
              </div>
              {metricsData?.aggregatedMetrics && (
                <div className="mt-6">
                  <p className="text-sm text-gray-500 mb-2">Métricas de Interação Agregadas</p>
                  <div className="h-64">
                    <Chart
                      type="radar"
                      data={prepareRadarData(metricsData.aggregatedMetrics)}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { stepSize: 20 }
                          }
                        },
                        plugins: {
                          legend: { position: 'top' },
                          tooltip: { mode: 'index' }
                        }
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {currentTab === 1 && (
            <div>
              <h4 className="text-lg font-medium text-gray-800 mb-4">Métricas de Interação</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard title="Precisão de Toque" value={metricsData?.touchInteractions?.accuracy || 0} suffix="%" />
                <MetricCard title="Tempo de Reação" value={metricsData?.touchInteractions?.reactionTime || 0} suffix="ms" />
                <MetricCard title="Consistência" value={metricsData?.touchInteractions?.consistency || 0} suffix="%" />
                <MetricCard title="Controle Fino" value={metricsData?.touchInteractions?.fineControl || 0} suffix="pts" />
              </div>
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700 mb-2">
                  <span className="mr-2" role="img" aria-label="Ícone de informação">ℹ️</span>
                  As métricas de interação são baseadas na análise de padrões de toque, pressão e tempo de resposta durante as atividades.
                </p>
                <p className="text-sm text-gray-500">
                  Uma maior consistência e precisão de toque podem indicar melhor coordenação motora fina.
                </p>
              </div>
              {metricsData?.touchInteractions?.history && (
                <div className="mt-6">
                  <p className="text-sm text-gray-500 mb-2">Evolução da Precisão de Toque</p>
                  <div className="h-64">
                    <Chart
                      type="line"
                      data={prepareLineData(metricsData.touchInteractions.history, 'Precisão (%)')}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true,
                            max: 100
                          }
                        },
                        plugins: {
                          legend: { position: 'top' },
                          tooltip: { mode: 'index' }
                        }
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {currentTab === 2 && (
            <div>
              <h4 className="text-lg font-medium text-gray-800 mb-4">Métricas de Sensores</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard
                  title="Acelerômetro"
                  value={metricsData?.deviceSensors?.accelerometer ? 'Ativo' : 'Inativo'}
                  color={metricsData?.deviceSensors?.accelerometer ? '#22c55e' : '#64748b'}
                />
                <MetricCard
                  title="Giroscópio"
                  value={metricsData?.deviceSensors?.gyroscope ? 'Ativo' : 'Inativo'}
                  color={metricsData?.deviceSensors?.gyroscope ? '#22c55e' : '#64748b'}
                />
                <MetricCard
                  title="Orientação"
                  value={metricsData?.deviceSensors?.orientation ? 'Ativo' : 'Inativo'}
                  color={metricsData?.deviceSensors?.orientation ? '#22c55e' : '#64748b'}
                />
                <MetricCard
                  title="Touch nationAvançado"
                  value={metricsData?.deviceSensors?.advancedTouch ? 'Ativo' : 'Inativo'}
                  color={metricsData?.deviceSensors?.advancedTouch ? '#22c55e' : '#64748b'}
                />
              </div>
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700 mb-2">
                  <span className="mr-2" role="img" aria-label="Ícone de informação">ℹ️</span>
                  Os sensores disponíveis dependem do dispositivo utilizado. Nem todos os dispositivos possuem todos os sensores.
                </p>
                <p className="text-sm text-gray-500">
                  Para uma experiência multissensorial completa, recomenda-se o uso de um dispositivo com acelerômetro e giroscópio.
                </p>
              </div>
              {metricsData?.deviceHandling?.steadiness && (
                <div className="mt-6">
                  <p className="text-sm text-gray-500 mb-2">Estabilidade de Manuseio do Dispositivo</p>
                  <div className="h-64">
                    <Chart
                      type="bar"
                      data={prepareBarData(metricsData.deviceHandling.steadiness, 'Estabilidade')}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true
                          }
                        },
                        plugins: {
                          legend: { position: 'top' },
                          tooltip: { mode: 'index' }
                        }
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
);

/**
 * Componente para cartão de métrica individual
 */
const MetricCard: React.FC<MetricCardProps> = ({ title, value, suffix = '', color = 'inherit' }) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <div className="text-sm font-medium text-gray-600">{title}</div>
      <div className={`text-lg font-semibold ${color === 'inherit' ? 'text-gray-800' : ''}`} style={{ color: color !== 'inherit' ? color : undefined }}>
        {value} {suffix && <span className="text-sm font-normal">{suffix}</span>}
      </div>
    </div>
  );
};

/**
 * Preparar dados para o gráfico radar
 */
const prepareRadarData = (metrics: MetricData['aggregatedMetrics']) => {
  if (!metrics) return { labels: [], datasets: [] };
  return {
    labels: ['Precisão', 'Tempo de Reação', 'Controle', 'Consistência', 'Coordenação'],
    datasets: [
      {
        label: 'Usuário',
        data: [
          metrics.accuracy,
          metrics.reactionTime,
          metrics.control,
          metrics.consistency,
          metrics.coordination
        ],
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
      },
      {
        label: 'Média',
        data: [
          metrics.avgAccuracy,
          metrics.avgReactionTime,
          metrics.avgControl,
          metrics.avgConsistency,
          metrics.avgCoordination
        ],
        backgroundColor: 'rgba(148, 163, 184, 0.2)',
        borderColor: 'rgba(148, 163, 184, 1)',
        borderWidth: 2,
      }
    ]
  };
};

/**
 * Preparar dados para gráfico de linha
 */
const prepareLineData = (history: Array<{ date: string; value: number }>, label: string) => {
  return {
    labels: history.map(item => item.date),
    datasets: [
      {
        label,
        data: history.map(item => item.value),
        fill: false,
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        tension: 0.4
      }
    ]
  };
};

/**
 * Preparar dados para gráfico de barras
 */
const prepareBarData = (data: Record<string, number>, label: string) => {
  return {
    labels: Object.keys(data),
    datasets: [
      {
        label,
        data: Object.values(data),
        backgroundColor: [
          'rgba(59, 130, 246, 0.7)',
          'rgba(34, 197, 94, 0.7)',
          'rgba(239, 68, 68, 0.7)',
          'rgba(168, 85, 247, 0.7)',
        ],
        borderWidth: 1
      }
    ]
  };
};

/**
 * Função para gerar dados mock para desenvolvimento
 */
const generateMockData = (userId: string, gameType?: string): MetricData => {
  const randomPercent = () => Math.floor(Math.random() * 100);
  const randomInRange = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min;

  const getDates = (count: number) => {
    const dates: string[] = [];
    const today = new Date();
    for (let i = count - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i * 3);
      dates.push(date.toLocaleDateString('pt-BR'));
    }
    return dates;
  };

  const dates = getDates(7);

  return {
    userId,
    gameType: gameType || 'all',
    summary: {
      sessions: randomInRange(5, 30),
      dataPoints: randomInRange(1000, 10000),
      sensorsAvailable: randomInRange(2, 4),
      lastUpdated: new Date().toISOString()
    },
    aggregatedMetrics: {
      accuracy: randomPercent(),
      reactionTime: randomPercent(),
      control: randomPercent(),
      consistency: randomPercent(),
      coordination: randomPercent(),
      avgAccuracy: 75,
      avgReactionTime: 70,
      avgControl: 65,
      avgConsistency: 60,
      avgCoordination: 68
    },
    touchInteractions: {
      accuracy: randomPercent(),
      reactionTime: randomInRange(200, 800),
      consistency: randomPercent(),
      fineControl: randomInRange(50, 100),
      history: dates.map(date => ({
        date,
        value: randomPercent()
      }))
    },
    deviceHandling: {
      stability: randomPercent(),
      steadiness: {
        'Muito Estável': randomInRange(10, 40),
        'Estável': randomInRange(20, 50),
        'Pouco Estável': randomInRange(5, 20),
        'Instável': randomInRange(0, 10)
      }
    },
    deviceSensors: {
      accelerometer: Math.random() > 0.2,
      gyroscope: Math.random() > 0.3,
      orientation: Math.random() > 0.1,
      advancedTouch: Math.random() > 0.4
    }
  };
};

MultisensoryMetricsPanel.propTypes = {
  userId: PropTypes.string.isRequired,
  gameType: PropTypes.string,
  sessionData: PropTypes.object
};

MetricCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  suffix: PropTypes.string,
  color: PropTypes.string
};

export default MultisensoryMetricsPanel;