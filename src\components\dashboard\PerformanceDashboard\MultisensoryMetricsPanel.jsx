/**
 * @file MultisensoryMetricsPanel.jsx
 * @description Painel para visualização de métricas multissensoriais no PerformanceDashboard
 */

import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Chart } from 'react-chartjs-2';
import styles from './styles.module.css';

/**
 * Ícones personalizados para substituir os do Material UI
 */
const InfoIcon = () => <span className={styles.icon}>ℹ️</span>;
const BarChartIcon = () => <span className={styles.icon}>📊</span>;
const DevicesIcon = () => <span className={styles.icon}>📱</span>;
const TouchAppIcon = () => <span className={styles.icon}>👆</span>;
    borderRadius: theme.shape.borderRadius,
    marginTop: theme.spacing(2)
  },
  tabContent: {
    padding: theme.spacing(2),
    paddingBottom: theme.spacing(4)
  },
  metricsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
    gap: theme.spacing(2),
    marginTop: theme.spacing(3)
  },
  metricCard: {
    padding: theme.spacing(2),
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    textAlign: 'center'
  },
  metricValue: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    color: theme.palette.primary.main
  }
}));

/**
 * Componente para exibição de métricas multissensoriais
 */
const MultisensoryMetricsPanel = ({ userId, gameType, sessionData = null }) => {
  const classes = useStyles();
  const [currentTab, setCurrentTab] = useState(0);
  const [metricsData, setMetricsData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Carregar dados multissensoriais
  const loadMultisensoryData = useCallback(async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      setError(null);

      // Se temos sessionData diretamente, usamos
      if (sessionData && sessionData.sensorMetrics) {
        setMetricsData(sessionData);
        return;
      }
      
      // Caso contrário, tentamos buscar do localStorage para teste/desenvolvimento
      const cachedData = localStorage.getItem(`multisensory_${userId}_${gameType || 'all'}`);
      if (cachedData) {
        setMetricsData(JSON.parse(cachedData));
        return;
      }
      
      // Implementação de um mock para demonstração
      // Em produção, aqui seria uma chamada API real
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulação de latência
      
      const mockData = generateMockData(userId, gameType);
      setMetricsData(mockData);
      
      // Cache para desenvolvimento
      localStorage.setItem(`multisensory_${userId}_${gameType || 'all'}`, JSON.stringify(mockData));
    } catch (err) {
      console.error('Erro ao carregar dados multissensoriais', err);
      setError('Não foi possível carregar os dados multissensoriais');
    } finally {
      setIsLoading(false);
    }
  }, [userId, gameType, sessionData]);
  
  // Carregar dados ao inicializar o componente
  useEffect(() => {
    loadMultisensoryData();
  }, [loadMultisensoryData]);
  
  // Mudança de tab
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };
  
  // Exibir estado vazio quando não há dados
  if (!metricsData && !isLoading) {
    return (
      <div className={styles.metricsPanelRoot}>
        <div className={styles.metricsHeader}>
          <h3 className={styles.metricsTitle}>Métricas Multissensoriais</h3>
        </div>
        <div className={styles.metricsDivider}></div>
        <div className={styles.metricsEmptyState}>
          <DevicesIcon />
          <p style={{ marginBottom: '12px', fontSize: '16px' }}>
            Não há dados multissensoriais disponíveis
          </p>
          <p style={{ marginBottom: '16px', fontSize: '14px', color: '#64748b' }}>
            Os dados são coletados automaticamente durante os jogos com multisensores habilitados
          </p>
          <button 
            className={styles.metricsButton}
            onClick={loadMultisensoryData}
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={styles.metricsPanelRoot}>
      {isLoading && (
        <div className={styles.metricsLoadingOverlay}>
          <div className={styles.metricsSpinner}></div>
        </div>
      )}
      
      <div className={styles.metricsHeader}>
        <h3 className={styles.metricsTitle}>Métricas Multissensoriais</h3>
        <div>
          <button 
            className={styles.metricsButtonSecondary}
            onClick={() => alert('As métricas multissensoriais permitem analisar como o usuário interage fisicamente com os jogos, fornecendo insights sobre padrões motores, estabilidade e consistência.')}
          >
            <InfoIcon /> Sobre estas métricas
          </button>
        </div>
      </div>
      
      <div className={styles.metricsDivider}></div>
      
      <div className={styles.metricsTabs}>
        <div 
          className={`${styles.metricsTab} ${currentTab === 0 ? styles.active : ''}`} 
          onClick={() => handleTabChange(null, 0)}
        >
          <BarChartIcon /> Visão Geral
        </div>
        <div 
          className={`${styles.metricsTab} ${currentTab === 1 ? styles.active : ''}`} 
          onClick={() => handleTabChange(null, 1)}
        >
          <TouchAppIcon /> Interação
        </div>
        <div 
          className={`${styles.metricsTab} ${currentTab === 2 ? styles.active : ''}`} 
          onClick={() => handleTabChange(null, 2)}
        >
          <DevicesIcon /> Sensores
        </div>
      </div>
      
      <div className={styles.tabContent}>
        {currentTab === 0 && (
          <div>
            <h4 style={{ marginBottom: '16px', fontSize: '18px' }}>
              Resumo de Métricas Multissensoriais
            </h4>
            
            <div className={styles.metricsGrid}>
              <MetricCard 
                title="Sessões" 
                value={metricsData?.summary?.sessions || 0} 
                suffix="total" 
              />
              <MetricCard 
                title="Pontos de Dados" 
                value={metricsData?.summary?.dataPoints || 0} 
                suffix="coletados" 
              />
              <MetricCard 
                title="Sensores Disponíveis" 
                value={metricsData?.summary?.sensorsAvailable || 0} 
                suffix="de 4" 
              />
              <MetricCard 
                title="Estabilidade" 
                value={metricsData?.deviceHandling?.stability || 0} 
                suffix="%" 
                color="primary"
              />
            </div>
            
            {metricsData?.aggregatedMetrics && (
              <Box className={classes.chart}>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Métricas de Interação Agregadas
                </Typography>
                <Chart 
                  type="radar" 
                  data={prepareRadarChartData(metricsData)}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    }
                  }}
                />
              </Box>
            )}
          </Box>
        )}
        
        {currentTab === 1 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Métricas de Interação do Usuário
            </Typography>
            
            <div className={classes.metricsGrid}>
              <MetricCard 
                title="Precisão de Toque" 
                value={metricsData?.userInteraction?.touchPrecision || 0} 
                suffix="%" 
              />
              <MetricCard 
                title="Consistência" 
                value={metricsData?.userInteraction?.touchConsistency || 0} 
                suffix="%" 
              />
              <MetricCard 
                title="Frequência de Toque" 
                value={metricsData?.userInteraction?.touchFrequency || 0} 
                suffix="/min" 
              />
              <MetricCard 
                title="Tempo de Resposta" 
                value={metricsData?.userInteraction?.responseTime || 0} 
                suffix="ms" 
              />
            </div>
            
            <Box className={classes.infoBox}>
              <Typography variant="body2" color="textSecondary">
                As métricas de interação mostram como o usuário interage com a tela do dispositivo.
                Maior precisão e consistência indicam melhor coordenação motora fina.
              </Typography>
            </Box>
            
            {metricsData?.userInteraction?.touchData && (
              <Box className={classes.chart}>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Padrões de Toque ao Longo do Tempo
                </Typography>
                <Chart 
                  type="line" 
                  data={prepareTouchChartData(metricsData)}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    }
                  }}
                />
              </Box>
            )}
          </Box>
        )}
        
        {currentTab === 2 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Dados dos Sensores
            </Typography>
            
            <div className={classes.metricsGrid}>
              <MetricCard 
                title="Acelerômetro" 
                value={metricsData?.sensorMetrics?.accelerometer?.available ? "Disponível" : "Indisponível"} 
                color={metricsData?.sensorMetrics?.accelerometer?.available ? "primary" : "error"}
              />
              <MetricCard 
                title="Giroscópio" 
                value={metricsData?.sensorMetrics?.gyroscope?.available ? "Disponível" : "Indisponível"} 
                color={metricsData?.sensorMetrics?.gyroscope?.available ? "primary" : "error"}
              />
              <MetricCard 
                title="Toque" 
                value={metricsData?.sensorMetrics?.touch?.available ? "Disponível" : "Indisponível"} 
                color={metricsData?.sensorMetrics?.touch?.available ? "primary" : "error"}
              />
              <MetricCard 
                title="Geolocalização" 
                value={metricsData?.sensorMetrics?.geolocation?.available ? "Disponível" : "Indisponível"} 
                color={metricsData?.sensorMetrics?.geolocation?.available ? "primary" : "error"}
              />
            </div>
            
            <Box className={classes.infoBox}>
              <Typography variant="body2" color="textSecondary">
                Os sensores coletam dados sobre como o dispositivo está sendo segurado e movimentado.
                Estes dados são utilizados para analisar padrões motores e estabilidade.
              </Typography>
            </Box>
            
            {metricsData?.deviceHandling && (
              <Box className={classes.chart}>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Estabilidade do Dispositivo
                </Typography>
                <Chart 
                  type="bar" 
                  data={prepareDeviceStabilityChartData(metricsData)}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100
                      }
                    }
                  }}
                />
              </Box>
            )}
          </Box>
        )}
      </div>
    </Paper>
  );
};

// Componente para cartão de métrica individual
const MetricCard = ({ title, value, suffix = '', color = 'inherit' }) => {  
  return (
    <div className={styles.metricCard}>
      <div className={styles.metricLabel}>
        {title}
      </div>
      <div className={styles.metricValue} style={{ color: color !== 'inherit' ? color : undefined }}>
        {value} {suffix && <span style={{ fontSize: '0.8rem' }}>{suffix}</span>}
      </div>
    </div>
  );
};

// Preparar dados para o gráfico radar
function prepareRadarChartData(metricsData) {
  return {
    labels: [
      'Precisão de Toque', 
      'Consistência', 
      'Estabilidade', 
      'Tempo de Resposta', 
      'Coordenação'
    ],
    datasets: [
      {
        label: 'Métricas Atuais',
        data: [
          metricsData?.userInteraction?.touchPrecision || 0,
          metricsData?.userInteraction?.touchConsistency || 0,
          metricsData?.deviceHandling?.stability || 0,
          100 - (metricsData?.userInteraction?.responseTime / 10) || 0, // Convertido para escala 0-100
          metricsData?.userInteraction?.coordination || 0
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgb(54, 162, 235)',
        pointBackgroundColor: 'rgb(54, 162, 235)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(54, 162, 235)'
      },
      {
        label: 'Média do Usuário',
        data: [
          metricsData?.averages?.touchPrecision || 0,
          metricsData?.averages?.touchConsistency || 0,
          metricsData?.averages?.stability || 0,
          100 - (metricsData?.averages?.responseTime / 10) || 0,
          metricsData?.averages?.coordination || 0
        ],
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgb(255, 99, 132)',
        pointBackgroundColor: 'rgb(255, 99, 132)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(255, 99, 132)'
      }
    ]
  };
}

// Preparar dados para o gráfico de padrões de toque
function prepareTouchChartData(metricsData) {
  const labels = Array.from({ length: 10 }, (_, i) => `Sessão ${i+1}`);
  
  return {
    labels,
    datasets: [
      {
        label: 'Precisão de Toque',
        data: metricsData.userInteraction.touchTrend?.precision || Array(10).fill(0),
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgb(54, 162, 235)',
        borderWidth: 2,
        tension: 0.3
      },
      {
        label: 'Consistência de Toque',
        data: metricsData.userInteraction.touchTrend?.consistency || Array(10).fill(0),
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgb(255, 99, 132)',
        borderWidth: 2,
        tension: 0.3
      }
    ]
  };
}

// Preparar dados para o gráfico de estabilidade do dispositivo
function prepareDeviceStabilityChartData(metricsData) {
  return {
    labels: ['Muito Instável', 'Instável', 'Moderado', 'Estável', 'Muito Estável'],
    datasets: [
      {
        label: 'Estabilidade do Dispositivo',
        data: metricsData.deviceHandling.stabilityDistribution || [5, 10, 20, 40, 25],
        backgroundColor: [
          'rgba(255, 99, 132, 0.5)',
          'rgba(255, 159, 64, 0.5)',
          'rgba(255, 205, 86, 0.5)',
          'rgba(75, 192, 192, 0.5)',
          'rgba(54, 162, 235, 0.5)'
        ],
        borderColor: [
          'rgb(255, 99, 132)',
          'rgb(255, 159, 64)',
          'rgb(255, 205, 86)',
          'rgb(75, 192, 192)',
          'rgb(54, 162, 235)'
        ],
        borderWidth: 1
      }
    ]
  };
}

// Gerar dados de exemplo para demonstração
function generateMockData(userId, gameType) {
  // Seed baseada no userId para consistência
  const seed = userId.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
  
  // Função para gerar número pseudoaleatório entre min e max
  const rand = (min, max) => {
    const x = Math.sin(seed) * 10000;
    const r = x - Math.floor(x);
    return Math.floor(r * (max - min + 1)) + min;
  };
  
  // Gerar valores entre 50 e 95 para métricas de precisão
  const touchPrecision = 50 + rand(0, 45);
  const touchConsistency = 50 + rand(0, 45);
  const stability = 50 + rand(0, 45);
  const coordination = 50 + rand(0, 45);
  
  // Sessões e pontos de dados
  const sessions = 1 + rand(0, 9);
  const dataPoints = sessions * (100 + rand(0, 900));
  
  // Sensores disponíveis (entre 1 e 4)
  const sensorsAvailable = 1 + rand(0, 3);
  
  // Gerar tendências para gráficos
  const generateTrend = (baseline, variance) => {
    return Array.from({ length: 10 }, (_, i) => {
      // Tendência crescente
      const value = baseline + (i * variance) + rand(-variance, variance);
      // Garantir entre 0 e 100
      return Math.max(0, Math.min(100, value));
    });
  };
  
  return {
    metadata: {
      userId,
      gameType: gameType || 'all',
      generatedAt: new Date().toISOString(),
      dataType: 'mock'
    },
    summary: {
      sessions,
      dataPoints,
      sensorsAvailable,
      lastUpdated: new Date().toISOString()
    },
    sensorMetrics: {
      accelerometer: {
        available: sensorsAvailable >= 1,
        dataPoints: sensorsAvailable >= 1 ? rand(100, 1000) : 0
      },
      gyroscope: {
        available: sensorsAvailable >= 2,
        dataPoints: sensorsAvailable >= 2 ? rand(100, 1000) : 0
      },
      touch: {
        available: sensorsAvailable >= 3,
        dataPoints: sensorsAvailable >= 3 ? rand(100, 1000) : 0
      },
      geolocation: {
        available: sensorsAvailable >= 4,
        dataPoints: sensorsAvailable >= 4 ? rand(10, 100) : 0
      }
    },
    userInteraction: {
      touchPrecision,
      touchConsistency,
      touchFrequency: 10 + rand(0, 40),
      responseTime: 200 + rand(0, 300),
      coordination,
      touchTrend: {
        precision: generateTrend(touchPrecision - 10, 2),
        consistency: generateTrend(touchConsistency - 10, 2)
      }
    },
    deviceHandling: {
      stability,
      suddenMovements: rand(0, 20),
      suddenMovementsPerMinute: rand(0, 5),
      stabilityDistribution: [
        rand(0, 10),  // Muito Instável
        rand(0, 20),  // Instável
        rand(0, 30),  // Moderado
        rand(0, 40),  // Estável
        rand(0, 30)   // Muito Estável
      ]
    },
    averages: {
      touchPrecision: touchPrecision - rand(-5, 5),
      touchConsistency: touchConsistency - rand(-5, 5),
      stability: stability - rand(-5, 5),
      responseTime: 200 + rand(0, 300),
      coordination: coordination - rand(-5, 5)
    }
  };
}

MultisensoryMetricsPanel.propTypes = {
  userId: PropTypes.string,
  gameType: PropTypes.string,
  sessionData: PropTypes.object
};

MetricCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  suffix: PropTypes.string,
  color: PropTypes.string
};

export default MultisensoryMetricsPanel;
