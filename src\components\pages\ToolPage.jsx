import React, { useState } from 'react'
import AccessibilityPanel from '../common/AccessibilityPanel'
import PremiumGate from '../dashboard/Premium/PremiumGate.jsx'
import { usePremium } from '../../context/PremiumContext.jsx'
import { getTTSText } from '../../data/ttsTexts'
import TextToSpeech from '../common/TextToSpeech'
import DashboardContainer from '../dashboard/DashboardContainer'

const ToolPage = ({ toolId, onBack }) => {
  const [showAccessibilityPanel, setShowAccessibilityPanel] = useState(false)
  const { canAccessDashboard, canAccessAIReports } = usePremium()

  // Definir quais ferramentas requerem premium
  const premiumTools = [
    'integrated-system-dashboard',
    'performance-dashboard',
    'admin-tools'
  ]

  // Verificar se a ferramenta atual requer premium
  const requiresPremium = premiumTools.includes(toolId)
  // Se for o painel de acessibilidade, mostrar o painel completo
  if (toolId === 'accessibility-panel') {
    return (
      <>
        {showAccessibilityPanel && (
          <AccessibilityPanel 
            onClose={() => setShowAccessibilityPanel(false)}
            isButton={false}
            showAsButton={false}
          />
        )}
        <div style={{
          padding: '2rem',
          textAlign: 'center',
          background: 'rgba(255, 255, 255, 0.95)',
          borderRadius: '1rem',
          margin: '2rem',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}>
          <h1 style={{ color: '#8b5cf6', marginBottom: '1rem' }}>
            ♿ Painel de Acessibilidade
          </h1>
          <TextToSpeech 
            text={getTTSText('tools', 'accessibility', 'title')}
            size="small"
          />
          <p style={{ color: '#666', marginBottom: '2rem', fontSize: '1.1rem' }}>
            Configure opções de acessibilidade avançadas
          </p>
          <div style={{ 
            padding: '3rem', 
            background: '#f8f9fa', 
            borderRadius: '0.5rem',
            marginBottom: '2rem',
            fontSize: '1.2rem'
          }}>
            ♿ Painel avançado para configuração de recursos de acessibilidade
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button
              onClick={() => setShowAccessibilityPanel(true)}
              style={{
                background: 'linear-gradient(135deg, #10b981, #06b6d4)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                padding: '0.75rem 2rem',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              ⚙️ Abrir Configurações
            </button>
            
            <button
              onClick={onBack}
              style={{
                background: 'linear-gradient(135deg, #8b5cf6, #ec4899)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                padding: '0.75rem 2rem',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              🏠 Voltar ao Menu Principal
            </button>
          </div>
        </div>
      </>
    )
  }  const toolInfo = {
    'integrated-system-dashboard': {
      title: 'Dashboard Integrado',
      description: 'Monitor central com IA e análise terapêutica',
      content: '🤖 Dashboard com métricas avançadas e inteligência artificial para acompanhamento do desenvolvimento.'
    },
    'user-profiles': {
      title: 'Perfis de Usuário',
      description: 'Gerencie diferentes perfis para toda a família',
      content: '👤 Sistema de perfis personalizados para cada membro da família.'
    },
    'performance-dashboard': {
      title: 'Dashboard de Desempenho',
      description: 'Visualize estatísticas e gráficos de progresso',
      content: '📊 Relatórios detalhados e gráficos de evolução do aprendizado.'
    },
    'accessibility-panel': {
      title: 'Painel de Acessibilidade',
      description: 'Configure opções de acessibilidade avançadas',
      content: '♿ Painel avançado para configuração de recursos de acessibilidade, incluindo TTS, alto contraste e mais.'
    },
    'admin-tools': {
      title: 'Ferramentas Administrativas',
      description: 'Configurações avançadas e ferramentas de sistema',
      content: '⚙️ Conjunto completo de ferramentas para administração e configuração do sistema.'
    },
    'admin': {
      title: 'Área Administrativa',
      description: 'Configurações avançadas do sistema',
      content: '👤 Painel administrativo para configurações e gerenciamento.'
    },
    'about': {
      title: 'Sobre o Portal Betina',
      description: 'História e informações sobre o projeto',
      content: 'ℹ️ Conheça a história da Betina e como este portal pode ajudar no desenvolvimento cognitivo.'
    }
  }
  const tool = toolInfo[toolId] || {
    title: 'Ferramenta não encontrada',
    description: 'Esta ferramenta ainda não foi implementada',
    content: '❌ Ferramenta não encontrada'
  }

  // Se requer premium e não tem acesso, mostrar premium gate
  if (requiresPremium && !canAccessDashboard()) {
    return (
      <PremiumGate
        feature="dashboard"
        title={`${tool.title} - Recurso Premium`}
        description={`${tool.description} Este recurso avançado está disponível apenas para usuários premium.`}
      />
    )
  }  // Conteúdo especial para dashboard e outras ferramentas
  const getDashboardContent = () => {
    // Mapear o toolId para o tab correto do DashboardContainer
    const dashboardMapping = {
      'integrated-system-dashboard': 'integrated',
      'performance-dashboard': 'performance',
      'neuropedagogical-dashboard': 'neuropedagogical',
      'multisensory-dashboard': 'multisensory',
      'ai-dashboard': 'relatorioA'
    }
    
    // Se for um dashboard, renderizar o DashboardContainer
    if (Object.keys(dashboardMapping).includes(toolId)) {
      return <DashboardContainer initialTab={dashboardMapping[toolId]} />
    }
    
    // Para outros tipos de ferramentas, manter o conteúdo estático
    return (
      <div className="tool-content">
        {tool.content}
      </div>
    )
  }
  return (
    <div className="tool-page">
      <div className="tool-header">
        <h1 className="tool-title">
          {tool.title}
          {requiresPremium && (
            <span className="tool-premium-badge">
              PREMIUM
            </span>
          )}
        </h1>
        <TextToSpeech 
          text={tool.description}
          size="small"
        />
        <p className="tool-description">
          {tool.description}
        </p>
      </div>
      
      {getDashboardContent()}
      
      <button
        onClick={onBack}
        className="tool-action-button"
      >
        🏠 Voltar ao Menu Principal
      </button>
    </div>
  )
}

export default ToolPage
