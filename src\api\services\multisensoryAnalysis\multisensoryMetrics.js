/**
 * @file MultisensoryMetricsCollector.jsx
 * @description Sistema multissensorial para coleta de dados de sensores móveis
 * @version 3.0.0 - Conforme PORTAL-BETINA-V3-ARQUITETURA.md
 */

import { v4 as uuidv4 } from 'uuid';
import { DatabaseIntegrator } from '../../database/services/DatabaseIntegrator.js';

/**
 * Enhanced logging system
 * @private
 */
const LogLevels = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  CRITICAL: 4,
};

/**
 * MultisensoryLogger - Sistema de log especializado
 * @private
 */
export class MultisensoryLogger {
  constructor(module = 'MultisensoryMetrics', minLevel = LogLevels.INFO) {
    this.module = module;
    this.minLevel = minLevel;
    this.errorHistory = [];
    this.maxErrors = 50; // Keep only last 50 errors
  }

  _formatMessage(level, message, data) {
    const timestamp = new Date().toISOString();
    const levelIcon = this._getLevelIcon(level);
    const dataString = data ? `: ${JSON.stringify(data)}` : '';
    return `${levelIcon} [${timestamp}][${this.module}] ${message}${dataString}`;
  }

  _getLevelIcon(level) {
    switch (level) {
      case LogLevels.DEBUG: return '🔍';
      case LogLevels.INFO: return 'ℹ️';
      case LogLevels.WARN: return '⚠️';
      case LogLevels.ERROR: return '❌';
      case LogLevels.CRITICAL: return '🚨';
      default: return '📝';
    }
  }

  debug(message, data) {
    if (this.minLevel <= LogLevels.DEBUG) {
      console.debug(this._formatMessage(LogLevels.DEBUG, message, data));
    }
  }

  info(message, data) {
    if (this.minLevel <= LogLevels.INFO) {
      console.info(this._formatMessage(LogLevels.INFO, message, data));
    }
  }

  warn(message, data) {
    if (this.minLevel <= LogLevels.WARN) {
      console.warn(this._formatMessage(LogLevels.WARN, message, data));
    }
  }

  error(message, error, data) {
    if (this.minLevel <= LogLevels.ERROR) {
      console.error(this._formatMessage(LogLevels.ERROR, message, data));
      
      // Store error for history
      this.errorHistory.push({
        message,
        error: error?.message || error,
        stack: error?.stack,
        data,
        timestamp: new Date().toISOString(),
      });
      
      // Trim error history to max size
      if (this.errorHistory.length > this.maxErrors) {
        this.errorHistory = this.errorHistory.slice(-this.maxErrors);
      }
    }
  }

  critical(message, error, data) {
    if (this.minLevel <= LogLevels.CRITICAL) {
      console.error(this._formatMessage(LogLevels.CRITICAL, message, data));
      
      // Always store critical errors
      this.errorHistory.push({
        message,
        error: error?.message || error,
        stack: error?.stack,
        data,
        timestamp: new Date().toISOString(),
        isCritical: true,
      });
      
      // Trim error history to max size
      if (this.errorHistory.length > this.maxErrors) {
        this.errorHistory = this.errorHistory.slice(-this.maxErrors);
      }
    }
  }
  
  getErrorHistory() {
    return [...this.errorHistory];
  }
}

/**
 * MultisensoryMetricsCollector - Sistema de coleta multissensoriais
 * Integração com SessionService existente conforme arquitetura V3
 */
export class MultisensoryMetricsCollector {
  constructor() {
    // Initialize logger
    this.logger = new MultisensoryLogger('MultisensoryMetricsCollector');
    
    // Initialize database integrator for persistence
    this.databaseIntegrator = new DatabaseIntegrator();
    
    // Base properties
    this.sessionId = null;
    this.userId = null;
    this.isCollecting = false;
    this.sensorData = [];
    this.collectionStartTime = null;
    this.deviceInfo = null;
    
    // Initialize sensor handlers
    this.sensorHandlers = {
      accelerometer: new AccelerometerHandler(this),
      gyroscope: new GyroscopeHandler(this),
      touch: new TouchMetricsHandler(this),
      geolocation: new GeolocationHandler(this),
      device: new DeviceContextHandler(this),
    };
    
    // Padrões de neurodivergência (conforme arquitetura)
    this.neurodivergencePatterns = {
      repetitiveMovements: 0,
      selfRegulation: 0,
      sensorySeekingLevel: 0,
      anxietyIndicators: 0,
      stimmingDetection: false,
    };
    
    // Configuração de sensores conforme especificação
    this.sensorConfig = {
      accelerometer: { enabled: false, frequency: 10 }, // Hz
      gyroscope: { enabled: false, frequency: 10 },
      touchMetrics: { enabled: true, trackPressure: true },
      geolocation: { enabled: false, accuracy: 'high' },
      deviceContext: { enabled: true, trackBattery: true },
    };
    
    // Performance optimizations
    this.dataBuffer = {
      accelerometer: [],
      gyroscope: [],
      touch: [],
      maxBufferSize: 100, // Limit buffer size to prevent memory issues
      flushThreshold: 50,  // Flush data when buffer reaches this size
    };
    
    this.throttleConfig = {
      lastProcessTime: 0,
      minInterval: 200, // Minimum time (ms) between processing events
    };
    
    // Performance metrics
    this.performanceMetrics = {
      dataPoints: 0,
      processedBatches: 0,
      totalProcessingTime: 0,
      averageProcessingTime: 0,
    };
  }

  /**
   * Inicia coleta de métricas multissensoriais
   * Integração com SessionService existente
   * @param {string} sessionId - ID único da sessão
   * @param {string} userId - ID do usuário
   * @param {Object} [options] - Opções adicionais de configuração
   * @returns {Promise<Object>} Status da inicialização
   * @throws {Error} Se parâmetros inválidos ou falha na inicialização
   */
  async startMetricsCollection(sessionId, userId, options = {}) {
    try {
      // Input validation
      if (!sessionId || typeof sessionId !== 'string') {
        throw new Error('Invalid sessionId: must be a non-empty string');
      }
      
      if (!userId || typeof userId !== 'string') {
        throw new Error('Invalid userId: must be a non-empty string');
      }
      
      this.logger.info('Starting metrics collection', { sessionId, userId });
      
      // Reset previous collection data
      this.sensorData = [];
      this.dataBuffer = {
        accelerometer: [],
        gyroscope: [],
        touch: [],
        maxBufferSize: options.maxBufferSize || 100,
        flushThreshold: options.flushThreshold || 50,
      };
      
      this.sessionId = sessionId;
      this.userId = userId;
      this.isCollecting = true;
      this.collectionStartTime = new Date().toISOString();
      this.sensorData = [];
      
      this.logger.info('Iniciando coleta de métricas', { sessionId, userId });
      
      // Detectar capacidades do dispositivo
      await this._detectDeviceCapabilities();
      
      // Iniciar coleta de sensores disponíveis
      await this._startSensorCollection();
      
      this.logger.info('Coleta iniciada com sucesso', {
        sessionId,
        sensorsEnabled: this._getEnabledSensors(),
        deviceCapabilities: this.deviceInfo,
      });
      
      return {
        success: true,
        sessionId,
        sensorsEnabled: this._getEnabledSensors(),
        deviceCapabilities: this.deviceInfo,
      };
    } catch (error) {
      this.logger.error('Erro ao iniciar coleta de métricas', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Para coleta e gera relatório multissensorial
   * Integração com SessionAnalyzer existente
   */
  async stopMetricsCollection() {
    try {
      if (!this.isCollecting) {
        return { success: false, error: 'Collection not active' };
      }
      
      this.isCollecting = false;
      const endTime = new Date().toISOString();
      
      this.logger.info('Parando coleta de métricas', { sessionId: this.sessionId });
      
      // Parar coleta de sensores
      await this._stopSensorCollection();
      
      // Analisar padrões coletados
      const analysis = this._analyzeCollectedData();
      
      // Gerar relatório conforme especificação
      const report = {
        sessionId: this.sessionId,
        userId: this.userId,
        startTime: this.collectionStartTime,
        endTime,
        duration: new Date(endTime) - new Date(this.collectionStartTime),
        sensorData: this.sensorData,
        neurodivergencePatterns: this.neurodivergencePatterns,
        analysis,
        deviceInfo: this.deviceInfo,
      };
      
      // 💾 SALVAR NO BANCO DE DADOS
      try {
        const saveResult = await this.databaseIntegrator.saveMultisensoryMetrics(
          this.userId,
          this.sessionId,
          {
            multisensoryData: report,
            sensorReadings: this.sensorData,
            neurodivergencePatterns: this.neurodivergencePatterns,
            analysisResults: analysis,
            sessionDuration: report.duration,
            deviceContext: this.deviceInfo
          }
        );
        
        this.logger.info('📊 Métricas multissensoriais salvas no banco de dados', {
          sessionId: this.sessionId,
          saveSuccess: saveResult.success,
          dataPoints: this.sensorData.length
        });
        
        // Adicionar informação de persistência ao relatório
        report.persistenceStatus = saveResult;
        
      } catch (saveError) {
        this.logger.error('❌ Erro ao salvar métricas no banco de dados', saveError);
        report.persistenceStatus = { success: false, error: saveError.message };
      }
      
      this.logger.info('Coleta parada e relatório gerado', {
        sessionId: this.sessionId,
        totalDataPoints: this.sensorData.length,
        analysis,
      });
      
      return {
        success: true,
        report,
        sessionId: this.sessionId,
        totalDataPoints: this.sensorData.length,
        analysis
      };
    } catch (error) {
      this.logger.error('Erro ao parar coleta de métricas', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Obtém métricas atuais em tempo real
   * Para integração com AdvancedMetricsEngine
   */
  async getCurrentMetrics() {
    if (!this.isCollecting) {
      return null;
    }
    
    return {
      timestamp: new Date().toISOString(),
      mobileSensors: await this._getCurrentSensorReadings(),
      neurodivergencePatterns: { ...this.neurodivergencePatterns },
      deviceContext: await this._getDeviceContext(),
    };
  }

  /**
   * Detecta capacidades do dispositivo
   * @private
   */
  async _detectDeviceCapabilities() {
    // Verificar se estamos no navegador ou Node.js
    const isBrowser = typeof window !== 'undefined' && typeof navigator !== 'undefined';
    
    if (isBrowser) {
      this.deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        sensors: {
          accelerometer: 'DeviceMotionEvent' in window,
          gyroscope: 'DeviceOrientationEvent' in window,
          geolocation: 'geolocation' in navigator,
          touch: 'ontouchstart' in window,
        },
        capabilities: {
          vibration: 'vibrate' in navigator,
          notifications: 'Notification' in window,
          battery: 'getBattery' in navigator,
        },
      };
      
      // Configurar sensores baseado nas capacidades
      this.sensorConfig.accelerometer.enabled = this.deviceInfo.sensors.accelerometer;
      this.sensorConfig.gyroscope.enabled = this.deviceInfo.sensors.gyroscope;
      this.sensorConfig.geolocation.enabled = this.deviceInfo.sensors.geolocation;
    } else {
      // Mock para ambiente Node.js/test
      this.deviceInfo = {
        userAgent: 'Node.js Test Environment',
        platform: 'Node.js',
        isMobile: false,
        sensors: {
          accelerometer: false,
          gyroscope: false,
          geolocation: false,
          touch: false,
        },
        capabilities: {
          vibration: false,
          notifications: false,
          battery: false,
        },
      };
      
      // Em ambiente de teste, habilitar sensores virtuais
      this.sensorConfig.accelerometer.enabled = true;
      this.sensorConfig.gyroscope.enabled = true;
      this.sensorConfig.geolocation.enabled = true;
    }
  }

  /**
   * Inicia coleta de sensores
   * @private
   */
  async _startSensorCollection() {
    const isBrowser = typeof window !== 'undefined';
    
    if (isBrowser) {
      // Acelerômetro e giroscópio
      if (this.sensorConfig.accelerometer.enabled) {
        window.addEventListener('devicemotion', this._handleDeviceMotion.bind(this));
      }
      
      if (this.sensorConfig.gyroscope.enabled) {
        window.addEventListener('deviceorientation', this._handleDeviceOrientation.bind(this));
      }
      
      // Touch metrics
      if (this.sensorConfig.touchMetrics.enabled) {
        document.addEventListener('touchstart', this._handleTouchStart.bind(this));
        document.addEventListener('touchmove', this._handleTouchMove.bind(this));
        document.addEventListener('touchend', this._handleTouchEnd.bind(this));
      }
      
      // Context tracking
      if (this.sensorConfig.deviceContext.enabled) {
        setInterval(() => this._collectDeviceContext(), 5000); // Cada 5 segundos
      }
    } else {
      // Em ambiente Node.js/test, simular a coleta de sensores
      this.logger.info('Ambiente Node.js detectado - simulando coleta de sensores');
      
      // Simular dados de sensores para testes
      this._simulateSensorData();
    }
  }

  /**
   * Simula dados de sensores para ambiente de teste
   * @private
   */
  _simulateSensorData() {
    // Simular dados de acelerômetro
    if (this.sensorConfig.accelerometer.enabled) {
      const mockAccelData = {
        x: Math.random() * 2 - 1,
        y: Math.random() * 2 - 1,
        z: Math.random() * 2 - 1,
        timestamp: Date.now()
      };
      this._handleDeviceMotion({ acceleration: mockAccelData });
    }
    
    // Simular dados de giroscópio
    if (this.sensorConfig.gyroscope.enabled) {
      const mockGyroData = {
        alpha: Math.random() * 360,
        beta: Math.random() * 360,
        gamma: Math.random() * 360,
        timestamp: Date.now()
      };
      this._handleDeviceOrientation(mockGyroData);
    }
  }

  /**
   * Para coleta de sensores
   * @private
   */
  async _stopSensorCollection() {
    const isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';
    
    if (isBrowser) {
      window.removeEventListener('devicemotion', this._handleDeviceMotion.bind(this));
      window.removeEventListener('deviceorientation', this._handleDeviceOrientation.bind(this));
      document.removeEventListener('touchstart', this._handleTouchStart.bind(this));
      document.removeEventListener('touchmove', this._handleTouchMove.bind(this));
      document.removeEventListener('touchend', this._handleTouchEnd.bind(this));
    } else {
      // Em ambiente Node.js/test, apenas log
      this.logger.info('Parando coleta de sensores simulada');
    }
  }

  /**
   * Handler para eventos de movimento do dispositivo
   * @private
   */
  _handleDeviceMotion(event) {
    if (!this.isCollecting) return;
    
    const motionData = {
      type: 'devicemotion',
      timestamp: new Date().toISOString(),
      acceleration: {
        x: event.acceleration?.x || 0,
        y: event.acceleration?.y || 0,
        z: event.acceleration?.z || 0,
      },
      accelerationIncludingGravity: {
        x: event.accelerationIncludingGravity?.x || 0,
        y: event.accelerationIncludingGravity?.y || 0,
        z: event.accelerationIncludingGravity?.z || 0,
      },
      rotationRate: {
        alpha: event.rotationRate?.alpha || 0,
        beta: event.rotationRate?.beta || 0,
        gamma: event.rotationRate?.gamma || 0,
      },
    };
    
    this.sensorData.push(motionData);
    this._analyzeMovementPatterns(motionData);
  }

  /**
   * Handler para eventos de orientação do dispositivo
   * @private
   */
  _handleDeviceOrientation(event) {
    if (!this.isCollecting) return;
    
    const orientationData = {
      type: 'deviceorientation',
      timestamp: new Date().toISOString(),
      alpha: event.alpha || 0,
      beta: event.beta || 0,
      gamma: event.gamma || 0,
      absolute: event.absolute || false,
    };
    
    this.sensorData.push(orientationData);
  }

  /**
   * Handlers para eventos de toque
   * @private
   */
  _handleTouchStart(event) {
    if (!this.isCollecting) return;
    
    Array.from(event.touches).forEach(touch => {
      const touchData = {
        type: 'touchstart',
        timestamp: new Date().toISOString(),
        identifier: touch.identifier,
        x: touch.clientX,
        y: touch.clientY,
        force: touch.force || 0,
        radiusX: touch.radiusX || 0,
        radiusY: touch.radiusY || 0,
      };
      
      this.sensorData.push(touchData);
    });
  }

  _handleTouchMove(event) {
    if (!this.isCollecting) return;
    
    Array.from(event.touches).forEach(touch => {
      const touchData = {
        type: 'touchmove',
        timestamp: new Date().toISOString(),
        identifier: touch.identifier,
        x: touch.clientX,
        y: touch.clientY,
        force: touch.force || 0,
      };
      
      this.sensorData.push(touchData);
    });
  }

  _handleTouchEnd(event) {
    if (!this.isCollecting) return;
    
    Array.from(event.changedTouches).forEach(touch => {
      const touchData = {
        type: 'touchend',
        timestamp: new Date().toISOString(),
        identifier: touch.identifier,
        x: touch.clientX,
        y: touch.clientY,
      };
      
      this.sensorData.push(touchData);
    });
  }

  /**
   * Analisa padrões de movimento para detectar neurodivergência
   * @private
   */
  _analyzeMovementPatterns(motionData) {
    // Detecção de movimentos repetitivos (stimming)
    const accelerationMagnitude = Math.sqrt(
      motionData.acceleration.x ** 2 +
      motionData.acceleration.y ** 2 +
      motionData.acceleration.z ** 2
    );
    
    // Análise simplificada para demo
    if (accelerationMagnitude > 2.0) {
      this.neurodivergencePatterns.repetitiveMovements += 0.1;
      this.neurodivergencePatterns.stimmingDetection = true;
    }
    
    // Normalizar valores entre 0-10
    this.neurodivergencePatterns.repetitiveMovements = Math.min(
      this.neurodivergencePatterns.repetitiveMovements, 10
    );
  }

  /**
   * Coleta contexto do dispositivo
   * @private
   */
  async _collectDeviceContext() {
    if (!this.isCollecting) return;
    
    const context = {
      type: 'devicecontext',
      timestamp: new Date().toISOString(),
      battery: await this._getBatteryInfo(),
      network: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt,
      } : null,
      time: {
        hour: new Date().getHours(),
        dayOfWeek: new Date().getDay(),
      },
    };
    
    this.sensorData.push(context);
  }

  /**
   * Obtém informações da bateria
   * @private
   */
  async _getBatteryInfo() {
    try {
      if ('getBattery' in navigator) {
        const battery = await navigator.getBattery();
        return {
          level: battery.level,
          charging: battery.charging,
          chargingTime: battery.chargingTime,
          dischargingTime: battery.dischargingTime,
        };
      }
    } catch (error) {
      console.warn('Battery API not available:', error);
    }
    return null;
  }

  /**
   * Obtém leituras atuais dos sensores
   * @private
   */
  async _getCurrentSensorReadings() {
    const recent = this.sensorData.slice(-10); // Últimas 10 leituras
    return {
      accelerometer: recent.filter(d => d.type === 'devicemotion').slice(-1)[0] || null,
      gyroscope: recent.filter(d => d.type === 'deviceorientation').slice(-1)[0] || null,
      touch: recent.filter(d => d.type.startsWith('touch')).slice(-3) || [],
      count: recent.length,
    };
  }

  /**
   * Obtém contexto atual do dispositivo
   * @private
   */
  async _getDeviceContext() {
    return {
      battery: await this._getBatteryInfo(),
      timestamp: new Date().toISOString(),
      isActive: this.isCollecting,
    };
  }

  /**
   * Lista sensores habilitados
   * @private
   */
  _getEnabledSensors() {
    return Object.entries(this.sensorConfig)
      .filter(([_, config]) => config.enabled)
      .map(([sensor, _]) => sensor);
  }

  /**
   * Analisa dados coletados
   * @private
   */
  _analyzeCollectedData() {
    const totalEvents = this.sensorData.length;
    const eventTypes = {};
    
    // Contar tipos de eventos
    this.sensorData.forEach(event => {
      eventTypes[event.type] = (eventTypes[event.type] || 0) + 1;
    });
    
    // Análise de padrões de toque
    const touchEvents = this.sensorData.filter(d => d.type.startsWith('touch'));
    const touchAnalysis = this._analyzeTouchPatterns(touchEvents);
    
    return {
      totalEvents,
      eventTypes,
      touchAnalysis,
      duration: this.collectionStartTime ? 
        new Date() - new Date(this.collectionStartTime) : 0,
      neurodivergenceIndicators: { ...this.neurodivergencePatterns },
    };
  }

  /**
   * Analisa padrões de toque
   * @private
   */
  _analyzeTouchPatterns(touchEvents) {
    if (touchEvents.length === 0) {
      return { touches: 0, avgPressure: 0, patterns: [] };
    }
    
    const pressures = touchEvents
      .filter(e => e.force !== undefined)
      .map(e => e.force);
    
    const avgPressure = pressures.length > 0 ? 
      pressures.reduce((a, b) => a + b, 0) / pressures.length : 0;
    
    return {
      touches: touchEvents.length,
      avgPressure,
      maxPressure: Math.max(...pressures, 0),
      touchTypes: {
        start: touchEvents.filter(e => e.type === 'touchstart').length,
        move: touchEvents.filter(e => e.type === 'touchmove').length,
        end: touchEvents.filter(e => e.type === 'touchend').length,
      },
    };
  }

  /**
   * Processa dados multissensoriais para integração com MetricsService
   * @param {Object} normalizedMetrics - Métricas normalizadas
   * @param {Object} multisensoryData - Dados multissensoriais brutos
   * @returns {Promise<Object>} - Análise multissensorial processada
   */
  /**
   * Processa dados multissensoriais e correlaciona com métricas de jogo
   * @param {Object} normalizedMetrics - Métricas normalizadas do jogo
   * @param {Object} multisensoryData - Dados dos sensores coletados
   * @returns {Promise<Object>} Resultado da análise multissensorial
   * @throws {Error} Se os dados forem inválidos ou ocorrer erro no processamento
   */
  async processMultisensoryData(normalizedMetrics, multisensoryData) {
    const processingStartTime = performance.now();
    
    try {
      // Input validation
      if (!normalizedMetrics || typeof normalizedMetrics !== 'object') {
        throw new Error('Invalid normalizedMetrics: must be a valid object');
      }
      
      if (!multisensoryData || !Array.isArray(multisensoryData) || multisensoryData.length === 0) {
        throw new Error('Invalid multisensoryData: must be a non-empty array');
      }
      
      // Check required fields in normalizedMetrics
      const requiredFields = ['gameId', 'sessionId', 'userId'];
      for (const field of requiredFields) {
        if (!normalizedMetrics[field]) {
          throw new Error(`Missing required field in normalizedMetrics: ${field}`);
        }
      }
      
      this.logger.info('Processando dados multissensoriais...', { 
        sessionId: this.sessionId,
        metricsCount: multisensoryData.length,
        gameId: normalizedMetrics.gameId 
      });
      
      // Apply throttling if needed
      const now = Date.now();
      if (now - this.throttleConfig.lastProcessTime < this.throttleConfig.minInterval) {
        this.logger.debug('Throttling applied to processMultisensoryData');
        await new Promise(resolve => setTimeout(resolve, this.throttleConfig.minInterval));
      }
      this.throttleConfig.lastProcessTime = now;
      
      // Analisar dados sensoriais
      const sensorAnalysis = await this._analyzeSensorData(multisensoryData);
      
      // Detectar padrões de neurodivergência
      const neurodivergenceAnalysis = this._analyzeNeurodivergencePatterns(multisensoryData);
      
      // Correlacionar com métricas do jogo
      const correlation = this._correlateWithGameMetrics(normalizedMetrics, multisensoryData);
      
      this.logger.info('Processamento concluído', {
        sessionId: this.sessionId,
        sensorAnalysis,
        neurodivergenceAnalysis,
        correlation,
      });
      
      return {
        timestamp: new Date().toISOString(),
        sensorData: sensorAnalysis,
        neurodivergencePatterns: neurodivergenceAnalysis,
        gameCorrelation: correlation,
        deviceContext: await this._getDeviceContext(),
        confidence: this._calculateAnalysisConfidence(sensorAnalysis, neurodivergenceAnalysis),
      };
    } catch (error) {
      this.logger.error('Erro ao processar dados multissensoriais', error);
      return {
        error: true,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Analisa dados dos sensores
   * @private
   */
  async _analyzeSensorData(multisensoryData) {
    const analysis = {
      accelerometer: null,
      gyroscope: null,
      touch: null,
      context: null,
    };

    if (multisensoryData.accelerometer) {
      analysis.accelerometer = {
        avgMagnitude: this._calculateAccelerometerMagnitude(multisensoryData.accelerometer),
        variability: this._calculateVariability(multisensoryData.accelerometer),
        patterns: this._detectMovementPatterns(multisensoryData.accelerometer),
      };
    }

    if (multisensoryData.gyroscope) {
      analysis.gyroscope = {
        orientation: this._analyzeOrientation(multisensoryData.gyroscope),
        stability: this._calculateOrientationStability(multisensoryData.gyroscope),
      };
    }

    if (multisensoryData.touch) {
      analysis.touch = {
        pressure: this._analyzeTouchPressure(multisensoryData.touch),
        patterns: this._analyzeTouchPatterns(multisensoryData.touch),
        frequency: this._calculateTouchFrequency(multisensoryData.touch),
      };
    }

    return analysis;
  }

  /**
   * Analisa padrões de neurodivergência
   * @private
   */
  _analyzeNeurodivergencePatterns(multisensoryData) {
    return {
      stimmingIndicators: this._detectStimmingPatterns(multisensoryData),
      sensorySeekingLevel: this._calculateSensorySeekingLevel(multisensoryData),
      regulationPatterns: this._analyzeRegulationPatterns(multisensoryData),
      anxietyIndicators: this._detectAnxietyIndicators(multisensoryData),
    };
  }

  /**
   * Correlaciona dados multissensoriais com métricas do jogo
   * @private
   */
  _correlateWithGameMetrics(gameMetrics, sensorData) {
    return {
      performanceCorrelation: this._calculatePerformanceCorrelation(gameMetrics, sensorData),
      attentionCorrelation: this._calculateAttentionCorrelation(gameMetrics, sensorData),
      engagementCorrelation: this._calculateEngagementCorrelation(gameMetrics, sensorData),
      stressCorrelation: this._calculateStressCorrelation(gameMetrics, sensorData),
    };
  }

  /**
   * Calcula confiança da análise
   * @private
   */
  _calculateAnalysisConfidence(sensorAnalysis, neurodivergenceAnalysis) {
    let confidence = 0;
    let factors = 0;

    if (sensorAnalysis.accelerometer) { confidence += 0.3; factors++; }
    if (sensorAnalysis.gyroscope) { confidence += 0.2; factors++; }
    if (sensorAnalysis.touch) { confidence += 0.3; factors++; }
    if (neurodivergenceAnalysis.stimmingIndicators) { confidence += 0.2; factors++; }

    return factors > 0 ? confidence / factors : 0.5;
  }

  // Métodos auxiliares simplificados para análise
  _calculateAccelerometerMagnitude(data) {
    if (!data || !Array.isArray(data)) return 0;
    return data.reduce((sum, reading) => {
      const magnitude = Math.sqrt(
        Math.pow(reading.x || 0, 2) + 
        Math.pow(reading.y || 0, 2) + 
        Math.pow(reading.z || 0, 2)
      );
      return sum + magnitude;
    }, 0) / data.length;
  }

  _calculateVariability(data) {
    if (!data || data.length < 2) return 0;
    const values = data.map(d => d.magnitude || 0);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  _detectMovementPatterns(data) {
    return {
      repetitive: this._calculateRepetitiveScore(data),
      erratic: this._calculateErraticScore(data),
      smooth: this._calculateSmoothScore(data),
    };
  }

  _analyzeOrientation(data) {
    if (!data || data.length === 0) return { stable: true, avgTilt: 0 };
    return {
      stable: this._isOrientationStable(data),
      avgTilt: this._calculateAverageTilt(data),
    };
  }

  _calculateOrientationStability(data) {
    return data && data.length > 0 ? 0.8 : 0;
  }

  _analyzeTouchPressure(data) {
    if (!data || data.length === 0) return { avg: 0, max: 0, variability: 0 };
    const pressures = data.map(d => d.pressure || 0);
    return {
      avg: pressures.reduce((sum, p) => sum + p, 0) / pressures.length,
      max: Math.max(...pressures),
      variability: this._calculateVariability(pressures.map(p => ({ magnitude: p }))),
    };
  }

  _calculateTouchFrequency(data) {
    return data ? data.length : 0;
  }

  _detectStimmingPatterns(data) {
    return {
      detected: false,
      confidence: 0.5,
      patterns: [],
    };
  }

  _calculateSensorySeekingLevel(data) {
    return 0.5; // Placeholder
  }

  _analyzeRegulationPatterns(data) {
    return {
      selfRegulation: 0.7,
      externalRegulation: 0.3,
    };
  }

  _detectAnxietyIndicators(data) {
    return {
      detected: false,
      level: 0.2,
      indicators: [],
    };
  }

  /**
   * Calcula correlação entre métricas de jogo e dados sensoriais para performance
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {Array} sensorData - Dados dos sensores
   * @returns {number} Valor de correlação entre 0-1
   */
  _calculatePerformanceCorrelation(gameMetrics, sensorData) {
    try {
      if (!gameMetrics || !sensorData || !Array.isArray(sensorData) || sensorData.length === 0) {
        this.logger.warn('Invalid inputs for performance correlation calculation');
        return 0.5; // Default neutral value
      }
      
      // Extract relevant metrics
      const gameScore = gameMetrics.score || 0;
      const accuracy = gameMetrics.accuracy || 0;
      const responseTime = gameMetrics.averageResponseTime || 500;
      
      // Normalize game metrics to 0-1 range
      const normalizedScore = Math.min(1, Math.max(0, gameScore / 1000)); // Assume max score is 1000
      const normalizedAccuracy = Math.min(1, Math.max(0, accuracy / 100));
      const normalizedResponseTime = Math.min(1, Math.max(0, 1 - (responseTime / 2000))); // Faster is better
      
      // Calculate touch stability from sensor data
      let touchStability = 0;
      const touchData = sensorData.filter(d => d.type === 'touch');
      if (touchData.length > 1) {
        // Calculate variation in touch positions
        const touchVariations = touchData.slice(1).map((touch, i) => {
          const prevTouch = touchData[i];
          return Math.sqrt(
            Math.pow(touch.x - prevTouch.x, 2) +
            Math.pow(touch.y - prevTouch.y, 2)
          );
        });
        
        // Lower variation means higher stability
        const avgVariation = touchVariations.reduce((sum, val) => sum + val, 0) / touchVariations.length;
        touchStability = Math.min(1, Math.max(0, 1 - (avgVariation / 500))); // Normalize to 0-1
      }
      
      // Calculate movement smoothness from accelerometer data
      let movementSmoothness = 0;
      const accelData = sensorData.filter(d => d.type === 'accelerometer');
      if (accelData.length > 1) {
        // Calculate jerk (rate of change of acceleration)
        const jerks = accelData.slice(1).map((accel, i) => {
          const prevAccel = accelData[i];
          const timeElapsed = (accel.timestamp - prevAccel.timestamp) / 1000; // in seconds
          return Math.sqrt(
            Math.pow((accel.x - prevAccel.x) / timeElapsed, 2) +
            Math.pow((accel.y - prevAccel.y) / timeElapsed, 2) +
            Math.pow((accel.z - prevAccel.z) / timeElapsed, 2)
          );
        });
        
        // Lower jerk means smoother movement
        const avgJerk = jerks.reduce((sum, val) => sum + val, 0) / jerks.length;
        movementSmoothness = Math.min(1, Math.max(0, 1 - (avgJerk / 50))); // Normalize to 0-1
      }
      
      // Calculate performance correlation as weighted average
      const weights = {
        score: 0.3,
        accuracy: 0.3,
        responseTime: 0.2,
        touchStability: 0.1,
        movementSmoothness: 0.1
      };
      
      const performanceCorrelation = 
        (normalizedScore * weights.score) +
        (normalizedAccuracy * weights.accuracy) +
        (normalizedResponseTime * weights.responseTime) +
        (touchStability * weights.touchStability) +
        (movementSmoothness * weights.movementSmoothness);
      
      this.logger.debug('Performance correlation calculated', {
        performanceCorrelation,
        components: {
          normalizedScore,
          normalizedAccuracy,
          normalizedResponseTime,
          touchStability,
          movementSmoothness
        }
      });
      
      return performanceCorrelation;
    } catch (error) {
      this.logger.error('Error calculating performance correlation', error);
      return 0.5; // Default neutral value on error
    }
  }

  /**
   * Calcula correlação de atenção baseada em dados sensoriais e de jogo
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {Array} sensorData - Dados dos sensores
   * @returns {number} Valor de correlação entre 0-1
   */
  _calculateAttentionCorrelation(gameMetrics, sensorData) {
    try {
      if (!gameMetrics || !sensorData || !Array.isArray(sensorData)) {
        return 0.5; // Default neutral value
      }
      
      // Extract metrics relevant to attention
      const consecutiveErrors = gameMetrics.consecutiveErrors || 0;
      const distractionCount = gameMetrics.distractionCount || 0;
      const focusLapses = gameMetrics.focusLapses || 0;
      
      // Normalized metrics (higher is better attention)
      const errorMetric = Math.min(1, Math.max(0, 1 - (consecutiveErrors / 5)));
      const distractionMetric = Math.min(1, Math.max(0, 1 - (distractionCount / 10)));
      const focusMetric = Math.min(1, Math.max(0, 1 - (focusLapses / 8)));
      
      // Sensor-based attention metrics
      let deviceMovementMetric = 0.5;
      let touchPrecisionMetric = 0.5;
      
      // Calculate device movement stability (less movement = better focus)
      const accelData = sensorData.filter(d => d.type === 'accelerometer');
      if (accelData.length > 5) {
        const movementVariations = accelData.map(d => 
          Math.sqrt(Math.pow(d.x, 2) + Math.pow(d.y, 2) + Math.pow(d.z, 2))
        );
        
        const avgMovement = movementVariations.reduce((sum, val) => sum + val, 0) / movementVariations.length;
        const movementStdDev = Math.sqrt(
          movementVariations.reduce((sum, val) => sum + Math.pow(val - avgMovement, 2), 0) / movementVariations.length
        );
        
        // More consistent movement pattern = better attention
        deviceMovementMetric = Math.min(1, Math.max(0, 1 - (movementStdDev / 3)));
      }
      
      // Calculate touch precision (consistent touch = better focus)
      const touchData = sensorData.filter(d => d.type === 'touch');
      if (touchData.length > 5) {
        // Calculate touch precision based on distance from target
        if (touchData[0].targetX !== undefined) {
          const touchPrecisions = touchData.map(d => {
            const distFromTarget = Math.sqrt(
              Math.pow(d.x - d.targetX, 2) + 
              Math.pow(d.y - d.targetY, 2)
            );
            return Math.max(0, 1 - (distFromTarget / 200));
          });
          
          touchPrecisionMetric = touchPrecisions.reduce((sum, val) => sum + val, 0) / touchPrecisions.length;
        }
      }
      
      // Weighted average for attention correlation
      const weights = {
        errorMetric: 0.25,
        distractionMetric: 0.2,
        focusMetric: 0.25,
        deviceMovementMetric: 0.15,
        touchPrecisionMetric: 0.15
      };
      
      const attentionCorrelation = 
        (errorMetric * weights.errorMetric) +
        (distractionMetric * weights.distractionMetric) +
        (focusMetric * weights.focusMetric) +
        (deviceMovementMetric * weights.deviceMovementMetric) +
        (touchPrecisionMetric * weights.touchPrecisionMetric);
        
      return attentionCorrelation;
    } catch (error) {
      this.logger.error('Error calculating attention correlation', error);
      return 0.5;
    }
  }

  /**
   * Calcula correlação de engajamento baseada em dados sensoriais e de jogo
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {Array} sensorData - Dados dos sensores
   * @returns {number} Valor de correlação entre 0-1
   */
  _calculateEngagementCorrelation(gameMetrics, sensorData) {
    try {
      if (!gameMetrics || !sensorData || !Array.isArray(sensorData)) {
        return 0.5;
      }
      
      // Game metrics relevant to engagement
      const playTime = gameMetrics.playTime || 0; // in seconds
      const interactionRate = gameMetrics.interactionRate || 0; // interactions per minute
      const completionRate = gameMetrics.completionRate || 0; // percentage of tasks completed
      
      // Normalized metrics (higher is better engagement)
      const playTimeMetric = Math.min(1, Math.max(0, playTime / 300)); // Normalize to 5 minutes max
      const interactionMetric = Math.min(1, Math.max(0, interactionRate / 60)); // Normalize to 1 interaction per second max
      const completionMetric = Math.min(1, Math.max(0, completionRate / 100));
      
      // Sensor-based engagement metrics
      let inputVarietyMetric = 0.5;
      let responsivenessFactor = 0.5;
      
      // Calculate input variety (more varied inputs = higher engagement)
      const touchEvents = sensorData.filter(d => d.type === 'touch');
      if (touchEvents.length > 5) {
        // Count unique screen areas touched (divide screen into a 3x3 grid)
        const screenAreas = new Set();
        touchEvents.forEach(event => {
          const gridX = Math.floor(event.x / (event.screenWidth / 3));
          const gridY = Math.floor(event.y / (event.screenHeight / 3));
          screenAreas.add(`${gridX},${gridY}`);
        });
        
        // More unique areas = more variety = higher engagement
        inputVarietyMetric = Math.min(1, Math.max(0, screenAreas.size / 9));
      }
      
      // Calculate responsiveness (quicker responses = higher engagement)
      if (gameMetrics.responseTimeSeries && Array.isArray(gameMetrics.responseTimeSeries)) {
        const avgResponseTime = gameMetrics.responseTimeSeries.reduce((sum, val) => sum + val, 0) 
          / gameMetrics.responseTimeSeries.length;
        
        // Normalize to 0-1 where faster is better (up to a point)
        responsivenessFactor = Math.min(1, Math.max(0, 1 - ((avgResponseTime - 300) / 2000)));
      }
      
      // Weighted average for engagement correlation
      const weights = {
        playTimeMetric: 0.2,
        interactionMetric: 0.25,
        completionMetric: 0.25,
        inputVarietyMetric: 0.15,
        responsivenessFactor: 0.15
      };
      
      const engagementCorrelation = 
        (playTimeMetric * weights.playTimeMetric) +
        (interactionMetric * weights.interactionMetric) +
        (completionMetric * weights.completionMetric) +
        (inputVarietyMetric * weights.inputVarietyMetric) +
        (responsivenessFactor * weights.responsivenessFactor);
        
      return engagementCorrelation;
    } catch (error) {
      this.logger.error('Error calculating engagement correlation', error);
      return 0.5;
    }
  }

  /**
   * Calcula correlação de estresse baseada em dados sensoriais e de jogo
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {Array} sensorData - Dados dos sensores
   * @returns {number} Valor de correlação entre 0-1 (maior valor = maior estresse)
   */
  _calculateStressCorrelation(gameMetrics, sensorData) {
    try {
      if (!gameMetrics || !sensorData || !Array.isArray(sensorData)) {
        return 0.3; // Default low-moderate stress
      }
      
      // Game metrics relevant to stress
      const errorRate = gameMetrics.errorRate || 0; // percentage of errors
      const timeouts = gameMetrics.timeoutCount || 0; // number of timeouts
      const retryCount = gameMetrics.retryCount || 0; // number of retries
      
      // Normalize metrics (higher value = more stress)
      const errorStress = Math.min(1, Math.max(0, errorRate / 100));
      const timeoutStress = Math.min(1, Math.max(0, timeouts / 5));
      const retryStress = Math.min(1, Math.max(0, retryCount / 8));
      
      // Sensor-based stress indicators
      let movementIntensity = 0.3;
      let touchPressureStress = 0.3;
      
      // Calculate movement intensity (higher acceleration variations = more stress)
      const accelData = sensorData.filter(d => d.type === 'accelerometer');
      if (accelData.length > 5) {
        const movementMagnitudes = accelData.map(d => 
          Math.sqrt(Math.pow(d.x, 2) + Math.pow(d.y, 2) + Math.pow(d.z, 2))
        );
        
        const avgMagnitude = movementMagnitudes.reduce((sum, val) => sum + val, 0) / movementMagnitudes.length;
        const magnitudeVariation = movementMagnitudes.reduce((sum, val) => sum + Math.abs(val - avgMagnitude), 0) / movementMagnitudes.length;
        
        // Higher variation = higher stress
        movementIntensity = Math.min(1, Math.max(0, magnitudeVariation / 5));
      }
      
      // Calculate touch pressure stress (higher pressure = more stress)
      const touchData = sensorData.filter(d => d.type === 'touch' && d.pressure !== undefined);
      if (touchData.length > 0) {
        const avgPressure = touchData.reduce((sum, d) => sum + (d.pressure || 0), 0) / touchData.length;
        touchPressureStress = Math.min(1, Math.max(0, avgPressure / 0.8)); // Normalize to 0-1 (0.8 is high pressure)
      }
      
      // Weighted average for stress correlation
      const weights = {
        errorStress: 0.25,
        timeoutStress: 0.2,
        retryStress: 0.2,
        movementIntensity: 0.2,
        touchPressureStress: 0.15
      };
      
      const stressCorrelation = 
        (errorStress * weights.errorStress) +
        (timeoutStress * weights.timeoutStress) +
        (retryStress * weights.retryStress) +
        (movementIntensity * weights.movementIntensity) +
        (touchPressureStress * weights.touchPressureStress);
        
      return stressCorrelation;
    } catch (error) {
      this.logger.error('Error calculating stress correlation', error);
      return 0.3;
    }
  }

  /**
   * Calcula pontuação de movimentos repetitivos nos dados sensoriais
   * @param {Array} data - Dados sensoriais
   * @returns {number} Pontuação entre 0-1
   */
  _calculateRepetitiveScore(data) {
    try {
      if (!data || !Array.isArray(data) || data.length < 10) {
        return 0.3; // Default score
      }
      
      // Extract relevant sensor data
      const accelData = data.filter(d => d.type === 'accelerometer');
      const gyroData = data.filter(d => d.type === 'gyroscope');
      
      // No sufficient data for analysis
      if (accelData.length < 10 && gyroData.length < 10) {
        return 0.3;
      }
      
      let repetitiveScore = 0;
      
      // Calculate repetitive motion score from accelerometer data
      if (accelData.length >= 10) {
        // Use Fourier Transform to detect repeating patterns
        const xValues = accelData.map(d => d.x || 0);
        const yValues = accelData.map(d => d.y || 0);
        const zValues = accelData.map(d => d.z || 0);
        
        // Calculate auto-correlation for each axis
        const xCorrelation = this._calculateAutocorrelation(xValues);
        const yCorrelation = this._calculateAutocorrelation(yValues);
        const zCorrelation = this._calculateAutocorrelation(zValues);
        
        // Higher correlation values indicate repeating patterns
        const avgCorrelation = (xCorrelation + yCorrelation + zCorrelation) / 3;
        repetitiveScore = Math.min(1, Math.max(0, avgCorrelation));
      }
      
      // Use gyroscope data as a fallback or to refine the score
      else if (gyroData.length >= 10) {
        // Similar approach using gyroscope data
        const xValues = gyroData.map(d => d.x || 0);
        const yValues = gyroData.map(d => d.y || 0);
        const zValues = gyroData.map(d => d.z || 0);
        
        const xCorrelation = this._calculateAutocorrelation(xValues);
        const yCorrelation = this._calculateAutocorrelation(yValues);
        const zCorrelation = this._calculateAutocorrelation(zValues);
        
        const avgCorrelation = (xCorrelation + yCorrelation + zCorrelation) / 3;
        repetitiveScore = Math.min(1, Math.max(0, avgCorrelation));
      }
      
      return repetitiveScore;
    } catch (error) {
      this.logger.error('Error calculating repetitive score', error);
      return 0.3;
    }
  }
  
  /**
   * Calcula auto-correlação de uma série temporal para detectar padrões repetitivos
   * @param {Array<number>} series - Série temporal
   * @returns {number} Valor de auto-correlação entre 0-1
   * @private
   */
  _calculateAutocorrelation(series) {
    try {
      // Simple implementation of auto-correlation
      // In a real implementation, use a proper signal processing library
      const n = series.length;
      if (n < 4) return 0;
      
      const mean = series.reduce((sum, val) => sum + val, 0) / n;
      const normalizedSeries = series.map(val => val - mean);
      
      // Calculate variance
      const variance = normalizedSeries.reduce((sum, val) => sum + val * val, 0) / n;
      if (variance === 0) return 0;
      
      // Calculate autocorrelation with lag-1
      let autocorr = 0;
      for (let i = 1; i < n; i++) {
        autocorr += normalizedSeries[i] * normalizedSeries[i-1];
      }
      autocorr /= ((n - 1) * variance);
      
      return Math.abs(autocorr); // Return absolute value as a measure of repetitiveness
    } catch (error) {
      this.logger.error('Error in autocorrelation calculation', error);
      return 0;
    }
  }
  
  /**
   * Calcula pontuação de movimento errático nos dados sensoriais
   * @param {Array} data - Dados sensoriais
   * @returns {number} Pontuação entre 0-1 (maior valor = mais errático)
   */
  _calculateErraticScore(data) {
    try {
      if (!data || !Array.isArray(data) || data.length < 5) {
        return 0.2; // Default score
      }
      
      // Extract relevant sensor data
      const accelData = data.filter(d => d.type === 'accelerometer');
      const touchData = data.filter(d => d.type === 'touch');
      
      if (accelData.length < 5 && touchData.length < 5) {
        return 0.2;
      }
      
      let erraticScore = 0;
      
      // Calculate erratic movement score from accelerometer data
      if (accelData.length >= 5) {
        // Calculate jerk (rate of change of acceleration) for each sample
        const jerks = [];
        for (let i = 1; i < accelData.length; i++) {
          const dt = (accelData[i].timestamp - accelData[i-1].timestamp) / 1000; // in seconds
          if (dt > 0) {
            const jerkX = Math.abs(accelData[i].x - accelData[i-1].x) / dt;
            const jerkY = Math.abs(accelData[i].y - accelData[i-1].y) / dt;
            const jerkZ = Math.abs(accelData[i].z - accelData[i-1].z) / dt;
            const jerkMagnitude = Math.sqrt(jerkX*jerkX + jerkY*jerkY + jerkZ*jerkZ);
            jerks.push(jerkMagnitude);
          }
        }
        
        if (jerks.length > 0) {
          // Calculate average and standard deviation of jerk
          const avgJerk = jerks.reduce((sum, j) => sum + j, 0) / jerks.length;
          const jerkVariability = Math.sqrt(
            jerks.reduce((sum, j) => sum + Math.pow(j - avgJerk, 2), 0) / jerks.length
          );
          
          // Higher jerk and variability = more erratic movement
          erraticScore = Math.min(1, Math.max(0, (avgJerk / 20) * 0.7 + (jerkVariability / 10) * 0.3));
        }
      }
      
      // Use touch data to refine or as fallback
      else if (touchData.length >= 5) {
        // Calculate touch position changes
        const touchChanges = [];
        for (let i = 1; i < touchData.length; i++) {
          const dx = touchData[i].x - touchData[i-1].x;
          const dy = touchData[i].y - touchData[i-1].y;
          const dt = (touchData[i].timestamp - touchData[i-1].timestamp) / 1000;
          
          if (dt > 0) {
            const velocity = Math.sqrt(dx*dx + dy*dy) / dt;
            touchChanges.push(velocity);
          }
        }
        
        if (touchChanges.length > 0) {
          // Calculate average and variability of touch velocities
          const avgVelocity = touchChanges.reduce((sum, v) => sum + v, 0) / touchChanges.length;
          const velocityVariability = Math.sqrt(
            touchChanges.reduce((sum, v) => sum + Math.pow(v - avgVelocity, 2), 0) / touchChanges.length
          );
          
          // Higher velocity and variability = more erratic interaction
          erraticScore = Math.min(1, Math.max(0, (avgVelocity / 500) * 0.5 + (velocityVariability / 300) * 0.5));
        }
      }
      
      return erraticScore;
    } catch (error) {
      this.logger.error('Error calculating erratic score', error);
      return 0.2;
    }
  }
  
  /**
   * Calcula pontuação de suavidade de movimento nos dados sensoriais
   * @param {Array} data - Dados sensoriais
   * @returns {number} Pontuação entre 0-1 (maior valor = mais suave)
   */
  _calculateSmoothScore(data) {
    try {
      // Inverse of erratic score for simplicity
      const erraticScore = this._calculateErraticScore(data);
      return 1 - erraticScore;
    } catch (error) {
      this.logger.error('Error calculating smooth score', error);
      return 0.8;
    }
  }
  
  /**
   * Verifica se a orientação do dispositivo é estável
   * @param {Array} data - Dados sensoriais
   * @returns {boolean} True se orientação estável, False caso contrário
   */
  _isOrientationStable(data) {
    try {
      if (!data || !Array.isArray(data) || data.length < 5) {
        return true; // Default assumption
      }
      
      const orientationData = data.filter(d => 
        d.type === 'deviceorientation' || d.type === 'gyroscope'
      );
      
      if (orientationData.length < 5) {
        return true; // Not enough data
      }
      
      // Calculate orientation stability
      let alphaDiffs = 0;
      let betaDiffs = 0;
      let gammaDiffs = 0;
      let count = 0;
      
      for (let i = 1; i < orientationData.length; i++) {
        const prev = orientationData[i-1];
        const curr = orientationData[i];
        
        if (prev.alpha !== undefined && curr.alpha !== undefined) {
          alphaDiffs += Math.abs(curr.alpha - prev.alpha);
          betaDiffs += Math.abs(curr.beta - prev.beta);
          gammaDiffs += Math.abs(curr.gamma - prev.gamma);
          count++;
        }
      }
      
      if (count === 0) {
        return true; // No comparable orientation data
      }
      
      // Calculate average differences
      const avgAlphaDiff = alphaDiffs / count;
      const avgBetaDiff = betaDiffs / count;
      const avgGammaDiff = gammaDiffs / count;
      
      // Stability threshold: consider stable if average changes are small
      const isStable = (avgAlphaDiff < 5 && avgBetaDiff < 5 && avgGammaDiff < 5);
      
      return isStable;
    } catch (error) {
      this.logger.error('Error checking orientation stability', error);
      return true; // Assume stable on error
    }
  }
  
  /**
   * Calcula inclinação média do dispositivo
   * @param {Array} data - Dados sensoriais
   * @returns {number} Inclinação média em graus
   */
  _calculateAverageTilt(data) {
    try {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return 0;
      }
      
      const orientationData = data.filter(d => 
        d.type === 'deviceorientation' || d.type === 'gyroscope'
      );
      
      if (orientationData.length === 0) {
        return 0;
      }
      
      // Calculate average beta (forward/backward tilt)
      let totalBeta = 0;
      let count = 0;
      
      for (const d of orientationData) {
        if (d.beta !== undefined) {
          totalBeta += d.beta;
          count++;
        }
      }
      
      return count > 0 ? totalBeta / count : 0;
    } catch (error) {
      this.logger.error('Error calculating average tilt', error);
      return 0;
    }
  }
  
  /**
   * Processa o início da validação de métricas
   * @param {Object} data - Dados da validação
   * @returns {Promise<void>}
   */
  async onValidationStarted(data) {
    try {
      if (!data || !data.sessionId) {
        console.warn('⚠️ [MultisensoryMetricsCollector] Dados inválidos para onValidationStarted:', data);
        return;
      }

      console.info(`🔍 [${new Date().toISOString()}] Início de validação de métricas detectado`, {
        sessionId: data.sessionId,
        metricsCount: data.metrics?.length || 0,
      });

      // Registrar evento de validação no histórico de métricas
      this.recordValidationEvent({
        type: 'validation_started',
        sessionId: data.sessionId,
        timestamp: data.timestamp || new Date().toISOString(),
        metricsCount: data.metrics?.length || 0,
      });
    } catch (error) {
      console.error('❌ [MultisensoryMetricsCollector] Erro em onValidationStarted:', error);
    }
  }

  /**
   * Processa a conclusão da validação de métricas
   * @param {Object} data - Resultados da validação
   * @returns {Promise<void>}
   */
  async onValidationCompleted(data) {
    try {
      if (!data || !data.sessionId) {
        console.warn('⚠️ [MultisensoryMetricsCollector] Dados inválidos para onValidationCompleted:', data);
        return;
      }

      console.info(`✅ [${new Date().toISOString()}] Validação de métricas concluída`, {
        sessionId: data.sessionId,
        isValid: data.results?.valid || false,
        metricsCount: data.metrics?.length || 0,
      });

      // Registrar evento de validação concluída no histórico de métricas
      this.recordValidationEvent({
        type: 'validation_completed',
        sessionId: data.sessionId,
        timestamp: data.timestamp || new Date().toISOString(),
        results: data.results || { valid: true },
        metricsCount: data.metrics?.length || 0,
      });

      // Atualizar estatísticas internas de validação se necessário
      this.updateValidationStatistics(data);
    } catch (error) {
      console.error('❌ [MultisensoryMetricsCollector] Erro em onValidationCompleted:', error);
    }
  }

  /**
   * Registra evento de validação no histórico
   * @param {Object} event - Evento de validação
   * @private
   */
  recordValidationEvent(event) {
    // Implementação do registro de eventos
    if (!this.validationEvents) {
      this.validationEvents = [];
    }
    this.validationEvents.push({
      ...event,
      recordedAt: new Date().toISOString(),
    });
    
    // Limitar o tamanho do histórico
    if (this.validationEvents.length > 100) {
      this.validationEvents = this.validationEvents.slice(-100);
    }
  }

  /**
   * Atualiza estatísticas internas de validação
   * @param {Object} data - Dados da validação
   * @private
   */
  updateValidationStatistics(data) {
    // Implementação das estatísticas de validação
    if (!this.validationStats) {
      this.validationStats = {
        total: 0,
        valid: 0,
        invalid: 0,
        lastUpdated: null,
      };
    }
    
    this.validationStats.total++;
    if (data.results?.valid) {
      this.validationStats.valid++;
    } else {
      this.validationStats.invalid++;
    }
    this.validationStats.lastUpdated = new Date().toISOString();
  }
}

/**
 * Sensor Handler base class
 * @private
 */
class SensorHandler {
  constructor(collector) {
    this.collector = collector;
    this.isAvailable = false;
    this.readings = [];
  }

  initialize() {
    // To be implemented by subclasses
    return true;
  }
  
  start() {
    // To be implemented by subclasses
    return true;
  }
  
  stop() {
    // To be implemented by subclasses
    return true;
  }
  
  onReading() {
    // To be implemented by subclasses
  }
  
  onError(event) {
    this.collector.logger.error(`${this.constructor.name} error`, event);
  }
  
  getLatestData() {
    return this.readings.length > 0 ? this.readings[this.readings.length - 1] : null;
  }
  
  getData(count = 10) {
    return this.readings.slice(-count);
  }
}

/**
 * Specialized handler for accelerometer data
 * @private
 */
class AccelerometerHandler extends SensorHandler {
  async initialize() {
    try {
      // Check if accelerometer is available in the browser
      if ('Accelerometer' in window) {
        this.sensor = new Accelerometer({ frequency: this.collector.sensorConfig.accelerometer.frequency });
        this.isAvailable = true;
        
        this.sensor.addEventListener('reading', () => this.onReading());
        this.sensor.addEventListener('error', (event) => this.onError(event));
        
        this.collector.logger.info('Accelerometer initialized successfully');
        return true;
      } else {
        this.isAvailable = false;
        this.collector.logger.warn('Accelerometer API not available in this browser');
        return false;
      }
    } catch (error) {
      this.isAvailable = false;
      this.collector.logger.error('Failed to initialize accelerometer', error);
      return false;
    }
  }
  
  start() {
    if (this.isAvailable && this.sensor) {
      try {
        this.sensor.start();
        this.collector.logger.info('Accelerometer started');
        return true;
      } catch (error) {
        this.collector.logger.error('Failed to start accelerometer', error);
        return false;
      }
    }
    return false;
  }
  
  stop() {
    if (this.isAvailable && this.sensor) {
      try {
        this.sensor.stop();
        this.collector.logger.info('Accelerometer stopped');
        return true;
      } catch (error) {
        this.collector.logger.error('Failed to stop accelerometer', error);
        return false;
      }
    }
    return false;
  }
  
  onReading() {
    if (this.sensor) {
      const reading = {
        x: this.sensor.x,
        y: this.sensor.y,
        z: this.sensor.z,
        timestamp: Date.now()
      };
      
      // Add to readings and buffer
      this.readings.push(reading);
      this.collector.dataBuffer.accelerometer.push(reading);
      
      // Check if buffer needs processing
      if (this.collector.dataBuffer.accelerometer.length >= this.collector.dataBuffer.flushThreshold) {
        this.collector.processBufferedData('accelerometer');
      }
    }
  }
}

/**
 * Specialized handler for gyroscope data
 * @private
 */
class GyroscopeHandler extends SensorHandler {
  async initialize() {
    try {
      // Check if gyroscope is available in the browser
      if ('Gyroscope' in window) {
        this.sensor = new Gyroscope({ frequency: this.collector.sensorConfig.gyroscope.frequency });
        this.isAvailable = true;
        
        this.sensor.addEventListener('reading', () => this.onReading());
        this.sensor.addEventListener('error', (event) => this.onError(event));
        
        this.collector.logger.info('Gyroscope initialized successfully');
        return true;
      } else {
        this.isAvailable = false;
        this.collector.logger.warn('Gyroscope API not available in this browser');
        return false;
      }
    } catch (error) {
      this.isAvailable = false;
      this.collector.logger.error('Failed to initialize gyroscope', error);
      return false;
    }
  }
  
  start() {
    if (this.isAvailable && this.sensor) {
      try {
        this.sensor.start();
        this.collector.logger.info('Gyroscope started');
        return true;
      } catch (error) {
        this.collector.logger.error('Failed to start gyroscope', error);
        return false;
      }
    }
    return false;
  }
  
  stop() {
    if (this.isAvailable && this.sensor) {
      try {
        this.sensor.stop();
        this.collector.logger.info('Gyroscope stopped');
        return true;
      } catch (error) {
        this.collector.logger.error('Failed to stop gyroscope', error);
        return false;
      }
    }
    return false;
  }
  
  onReading() {
    if (this.sensor) {
      const reading = {
        alpha: this.sensor.alpha,
        beta: this.sensor.beta,
        gamma: this.sensor.gamma,
        timestamp: Date.now()
      };
      
      // Add to readings and buffer
      this.readings.push(reading);
      this.collector.dataBuffer.gyroscope.push(reading);
      
      // Check if buffer needs processing
      if (this.collector.dataBuffer.gyroscope.length >= this.collector.dataBuffer.flushThreshold) {
        this.collector.processBufferedData('gyroscope');
      }
    }
  }
}

/**
 * Specialized handler for touch metrics
 * @private
 */
class TouchMetricsHandler extends SensorHandler {
  initialize() {
    return true; // Touch doesn't need initialization like sensors
  }
  
  start() {
    if (!this.isTracking) {
      try {
        window.addEventListener('touchstart', this.onTouchStart.bind(this));
        window.addEventListener('touchmove', this.onTouchMove.bind(this));
        window.addEventListener('touchend', this.onTouchEnd.bind(this));
        this.isTracking = true;
        this.collector.logger.info('Touch metrics tracking started');
        return true;
      } catch (error) {
        this.collector.logger.error('Failed to start touch metrics', error);
        return false;
      }
    }
    return true;
  }
  
  stop() {
    if (this.isTracking) {
      try {
        window.removeEventListener('touchstart', this.onTouchStart.bind(this));
        window.removeEventListener('touchmove', this.onTouchMove.bind(this));
        window.removeEventListener('touchend', this.onTouchEnd.bind(this));
        this.isTracking = false;
        this.collector.logger.info('Touch metrics tracking stopped');
        return true;
      } catch (error) {
        this.collector.logger.error('Failed to stop touch metrics', error);
        return false;
      }
    }
    return true;
  }
  
  // Touch event handlers
  // ...
}

/**
 * Specialized handler for geolocation
 * @private
 */
class GeolocationHandler extends SensorHandler {
  // Implementation...
}

/**
 * Specialized handler for device context (battery, network, etc.)
 * @private
 */
class DeviceContextHandler extends SensorHandler {
  // Implementation...
}

/**
 * Verifica a disponibilidade de sensores no dispositivo atual
 * @returns {Promise<Object>} Status de disponibilidade dos sensores
 */
async function checkSensorAvailability() {
  const sensorStatus = {
    accelerometer: false,
    gyroscope: false,
    touch: true, // Touch is generally available
    geolocation: false,
    deviceMotion: false,
    deviceOrientation: false,
    batteryStatus: false,
    networkInfo: false,
  };

  try {
    // Check for Permission API support
    const hasPermissionsAPI = 'permissions' in navigator;
    
    // Check Sensor API support
    if ('Accelerometer' in window) {
      sensorStatus.accelerometer = true;
      
      // Check permissions if possible
      if (hasPermissionsAPI) {
        try {
          const permission = await navigator.permissions.query({ name: 'accelerometer' });
          sensorStatus.accelerometer = permission.state === 'granted';
        } catch (error) {
          // Some browsers support the API but not all sensor permissions
          this.logger.debug('Accelerometer permission query not supported', { error: error.message });
        }
      }
    }
    
    // Gyroscope availability check
    if ('Gyroscope' in window) {
      sensorStatus.gyroscope = true;
      
      if (hasPermissionsAPI) {
        try {
          const permission = await navigator.permissions.query({ name: 'gyroscope' });
          sensorStatus.gyroscope = permission.state === 'granted';
        } catch (error) {
          this.logger.debug('Gyroscope permission query not supported', { error: error.message });
        }
      }
    }
    
    // Geolocation availability check
    if ('geolocation' in navigator) {
      sensorStatus.geolocation = true;
      
      if (hasPermissionsAPI) {
        try {
          const permission = await navigator.permissions.query({ name: 'geolocation' });
          sensorStatus.geolocation = permission.state === 'granted';
        } catch (error) {
          this.logger.debug('Geolocation permission query not supported', { error: error.message });
        }
      }
    }
    
    // Device motion and orientation
    if ('DeviceMotionEvent' in window) {
      sensorStatus.deviceMotion = true;
    }
    
    if ('DeviceOrientationEvent' in window) {
      sensorStatus.deviceOrientation = true;
    }
    
    // Battery status
    if ('getBattery' in navigator) {
      sensorStatus.batteryStatus = true;
    }
    
    // Network information
    if ('connection' in navigator) {
      sensorStatus.networkInfo = true;
    }
    
    this.logger.info('Sensor availability check completed', { sensorStatus });
    return sensorStatus;
    
  } catch (error) {
    this.logger.error('Error checking sensor availability', error);
    return sensorStatus;
  }
}

/**
 * Implementa fallbacks para sensores indisponíveis
 * @param {Object} sensorStatus - Status de disponibilidade dos sensores
 * @returns {Promise<void>}
 */
async function implementSensorFallbacks(sensorStatus) {
  try {
    // If hardware accelerometer is not available, use device motion as fallback
    if (!sensorStatus.accelerometer && sensorStatus.deviceMotion) {
      this.logger.info('Using DeviceMotion as fallback for Accelerometer');
      
      this.sensorHandlers.accelerometer.useDeviceMotionFallback = true;
      
      // Set up device motion event listener
      window.addEventListener('devicemotion', (event) => {
        const reading = {
          x: event.accelerationIncludingGravity?.x || 0,
          y: event.accelerationIncludingGravity?.y || 0,
          z: event.accelerationIncludingGravity?.z || 0,
          timestamp: Date.now()
        };
        
        this.dataBuffer.accelerometer.push(reading);
        
        // Check if buffer needs processing
        if (this.dataBuffer.accelerometer.length >= this.dataBuffer.flushThreshold) {
          this.processBufferedData('accelerometer');
        }
      });
    }
    
    // Similar fallbacks for other sensors
    // ...
    
    this.logger.info('Sensor fallbacks implemented successfully');
  } catch (error) {
    this.logger.error('Error implementing sensor fallbacks', error);
  }
}

export default MultisensoryMetricsCollector;