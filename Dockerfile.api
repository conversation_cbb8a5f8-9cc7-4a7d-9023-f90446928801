# Portal Betina V3 API - Dockerfile
# Configuração otimizada para API Node.js com resiliência

FROM node:20-alpine

# Instalar dependências do sistema
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    linux-headers

# Criar diretório da aplicação
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./

# Instalar dependências de produção apenas
ENV NODE_ENV=production
RUN npm install --omit=dev && \
    npm install csurf sanitize-html && \
    npm cache clean --force

# Copiar código-fonte da aplicação
COPY . .

# Verificar se diretório src existe
RUN ls -la src/ || (echo "Source directory not found" && exit 1)

# Criar diretório para logs
RUN mkdir -p /app/logs && \
    chmod 777 /app/logs

# Expor porta da API
EXPOSE 3000

# Healthcheck para garantir que API está respondendo
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
    CMD node -e "try{require('http').request({host:'localhost',port:3000,path:'/api/health',timeout:2000},(r)=>{r.statusCode===200?process.exit(0):process.exit(1)}).end()}catch(e){process.exit(1)}"

# Iniciar API com configurações otimizadas para produção
CMD ["node", "--max-old-space-size=2048", "src/api/server.js"]
