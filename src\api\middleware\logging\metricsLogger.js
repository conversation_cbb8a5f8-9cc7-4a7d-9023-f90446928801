/**
 * Metrics Logger Middleware
 * Portal Betina V3 - Sistema Terapêutico para Neurodivergência
 *
 * Middleware responsável por coletar e registrar métricas detalhadas
 * do sistema para monitoramento e análise de performance terapêutica.
 */

import path from 'path'
import winston from 'winston'

// Configuração do logger de métricas
const metricsLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'metrics.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10
    })
  ]
})

// Armazenamento em memória para métricas em tempo real
const metricsCache = {
  requests: new Map(),
  performance: new Map(),
  therapeutic: new Map(),
  system: new Map()
}

/**
 * Middleware principal para coleta de métricas
 * Registra dados de performance, uso e atividade terapêutica
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
const collectMetrics = (req, res, next) => {
  const startTime = Date.now()
  const requestId = generateRequestId()

  // Marcar início da requisição
  req.metrics = {
    requestId,
    startTime,
    endpoint: req.originalUrl,
    method: req.method,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  }

  // Interceptar resposta para coletar métricas finais
  const originalSend = res.send
  res.send = function (data) {
    const endTime = Date.now()
    const duration = endTime - startTime

    // Coletar métricas da requisição
    const requestMetrics = {
      requestId,
      timestamp: new Date().toISOString(),
      method: req.method,
      endpoint: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      contentLength: Buffer.byteLength(data || ''),
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id,
      userRole: req.user?.role,
      therapeutic: extractTherapeuticMetrics(req, res)
    }

    // Registrar métricas
    logRequestMetrics(requestMetrics)

    // Armazenar em cache para análise em tempo real
    cacheMetrics('requests', requestId, requestMetrics)

    // Verificar se é uma requisição terapêutica crítica
    if (isTherapeuticEndpoint(req.originalUrl)) {
      logTherapeuticMetrics(requestMetrics, req, data)
    }

    return originalSend.call(this, data)
  }

  next()
}

/**
 * Extrai métricas específicas do contexto terapêutico
 *
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Métricas terapêuticas
 */
function extractTherapeuticMetrics (req, res) {
  const therapeutic = {}

  // Identificar contexto terapêutico
  if (req.body?.childId || req.params?.childId) {
    therapeutic.childId = req.body?.childId || req.params?.childId
  }

  if (req.body?.sessionId || req.params?.sessionId) {
    therapeutic.sessionId = req.body?.sessionId || req.params?.sessionId
  }

  if (req.body?.gameId || req.params?.gameId) {
    therapeutic.gameId = req.body?.gameId || req.params?.gameId
  }

  // Extrair métricas específicas por tipo de endpoint
  if (req.originalUrl.includes('/metrics/')) {
    therapeutic.metricsType = extractMetricsType(req.originalUrl)
    therapeutic.dataPoints = req.body?.metrics?.length || 0
  }

  if (req.originalUrl.includes('/games/')) {
    therapeutic.gameAction = extractGameAction(req.method, req.originalUrl)
    therapeutic.gameLevel = req.body?.level || req.query?.level
  }

  if (req.originalUrl.includes('/analysis/')) {
    therapeutic.analysisType = extractAnalysisType(req.originalUrl)
    therapeutic.processingComplexity = calculateProcessingComplexity(req.body)
  }

  return therapeutic
}

/**
 * Registra métricas de requisição no log
 *
 * @param {Object} metrics - Métricas da requisição
 */
function logRequestMetrics (metrics) {
  metricsLogger.info('Request Metrics', {
    type: 'REQUEST_METRICS',
    ...metrics
  })
}

/**
 * Registra métricas específicas do sistema terapêutico
 *
 * @param {Object} requestMetrics - Métricas da requisição
 * @param {Object} req - Request object
 * @param {*} responseData - Dados da resposta
 */
function logTherapeuticMetrics (requestMetrics, req, responseData) {
  const therapeuticMetrics = {
    type: 'THERAPEUTIC_METRICS',
    timestamp: new Date().toISOString(),
    requestId: requestMetrics.requestId,
    endpoint: req.originalUrl,
    therapeuticContext: requestMetrics.therapeutic,
    performance: {
      duration: requestMetrics.duration,
      statusCode: requestMetrics.statusCode
    },
    engagement: calculateEngagementMetrics(req, responseData),
    accessibility: assessAccessibilityImpact(req),
    dataQuality: assessDataQuality(req.body, responseData)
  }

  metricsLogger.info('Therapeutic Activity', therapeuticMetrics)
  cacheMetrics('therapeutic', requestMetrics.requestId, therapeuticMetrics)
}

/**
 * Registra métricas de performance do sistema
 *
 * @param {Object} performanceData - Dados de performance
 */
const logPerformanceMetrics = (performanceData) => {
  const performanceMetrics = {
    type: 'PERFORMANCE_METRICS',
    timestamp: new Date().toISOString(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    uptime: process.uptime(),
    activeConnections: performanceData.activeConnections || 0,
    databaseConnections: performanceData.databaseConnections || 0,
    ...performanceData
  }

  metricsLogger.info('System Performance', performanceMetrics)
  cacheMetrics('performance', Date.now(), performanceMetrics)
}

/**
 * Registra métricas de uso de recursos do sistema
 *
 * @param {Object} resourceData - Dados de recursos
 */
const logResourceMetrics = (resourceData) => {
  const resourceMetrics = {
    type: 'RESOURCE_METRICS',
    timestamp: new Date().toISOString(),
    diskUsage: resourceData.diskUsage || 0,
    networkIO: resourceData.networkIO || { input: 0, output: 0 },
    activeUsers: resourceData.activeUsers || 0,
    concurrentSessions: resourceData.concurrentSessions || 0,
    queueSize: resourceData.queueSize || 0,
    cacheHitRate: resourceData.cacheHitRate || 0
  }

  metricsLogger.info('Resource Usage', resourceMetrics)
  cacheMetrics('system', Date.now(), resourceMetrics)
}

/**
 * Obtém métricas em tempo real do cache
 *
 * @param {string} type - Tipo de métrica
 * @param {number} limit - Limite de registros
 * @returns {Array} Métricas em tempo real
 */
const getRealTimeMetrics = (type = 'all', limit = 100) => {
  if (type === 'all') {
    return {
      requests: Array.from(metricsCache.requests.values()).slice(-limit),
      performance: Array.from(metricsCache.performance.values()).slice(-limit),
      therapeutic: Array.from(metricsCache.therapeutic.values()).slice(-limit),
      system: Array.from(metricsCache.system.values()).slice(-limit)
    }
  }

  return Array.from(metricsCache[type]?.values() || []).slice(-limit)
}

/**
 * Limpa cache de métricas antigas
 * Mantém apenas os dados mais recentes para performance
 */
const cleanMetricsCache = () => {
  const maxCacheSize = 1000

  Object.keys(metricsCache).forEach(type => {
    const cache = metricsCache[type]
    if (cache.size > maxCacheSize) {
      const entries = Array.from(cache.entries())
      const toKeep = entries.slice(-maxCacheSize)
      cache.clear()
      toKeep.forEach(([key, value]) => cache.set(key, value))
    }
  })
}

// Utilitários auxiliares
function generateRequestId () {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

function isTherapeuticEndpoint (url) {
  const therapeuticPaths = ['/metrics/', '/games/', '/analysis/', '/therapeutic/', '/progress/']
  return therapeuticPaths.some(path => url.includes(path))
}

function extractMetricsType (url) {
  const match = url.match(/\/metrics\/([^/?]+)/)
  return match ? match[1] : 'unknown'
}

function extractGameAction (method, url) {
  if (method === 'POST' && url.includes('/start')) return 'game_start'
  if (method === 'PUT' && url.includes('/progress')) return 'game_progress'
  if (method === 'POST' && url.includes('/complete')) return 'game_complete'
  return 'game_interaction'
}

function extractAnalysisType (url) {
  const match = url.match(/\/analysis\/([^/?]+)/)
  return match ? match[1] : 'general'
}

function calculateProcessingComplexity (data) {
  if (!data) return 'low'
  const dataSize = JSON.stringify(data).length
  if (dataSize > 10000) return 'high'
  if (dataSize > 1000) return 'medium'
  return 'low'
}

function calculateEngagementMetrics (req, responseData) {
  // Implementação simplificada - expandir conforme necessário
  return {
    interactionType: req.method,
    dataComplexity: calculateProcessingComplexity(req.body),
    responseSize: Buffer.byteLength(responseData || '')
  }
}

function assessAccessibilityImpact (req) {
  // Verificar se a requisição envolve recursos de acessibilidade
  const accessibilityFeatures = []

  if (req.body?.accessibility || req.query?.accessibility) {
    accessibilityFeatures.push('accessibility_settings')
  }

  if (req.body?.visualSupport || req.query?.visualSupport) {
    accessibilityFeatures.push('visual_support')
  }

  if (req.body?.audioSupport || req.query?.audioSupport) {
    accessibilityFeatures.push('audio_support')
  }

  return {
    featuresUsed: accessibilityFeatures,
    impactLevel: accessibilityFeatures.length > 0 ? 'high' : 'low'
  }
}

function assessDataQuality (requestData, responseData) {
  return {
    requestComplete: !!requestData && Object.keys(requestData).length > 0,
    responseSuccess: !!responseData,
    dataIntegrity: 'good' // Implementar validação mais robusta
  }
}

function cacheMetrics (type, key, data) {
  if (!metricsCache[type]) {
    metricsCache[type] = new Map()
  }
  metricsCache[type].set(key, data)
}

// Limpeza automática do cache a cada 5 minutos
setInterval(cleanMetricsCache, 5 * 60 * 1000)

export {
  collectMetrics,
  logPerformanceMetrics,
  logResourceMetrics,
  getRealTimeMetrics,
  cleanMetricsCache,
  metricsLogger
}

export default metricsLogger
