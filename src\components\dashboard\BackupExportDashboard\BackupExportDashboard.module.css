/* Dashboard Container */
.dashboardContainer {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  color: #ffffff;
  padding: 2rem;
  overflow-y: auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.headerContent {
  flex: 1;
}

.title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.titleIcon {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.5));
}

.subtitle {
  margin: 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.premiumBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #000;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.premiumIcon {
  font-size: 1.2rem;
}

/* Alertas */
.alert {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 12px;
  border: 1px solid;
  position: relative;
}

.alert.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: #22c55e;
  color: #22c55e;
}

.alert.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #ef4444;
}

.alert.info {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #3b82f6;
}

.alertIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.alertMessage {
  flex: 1;
  font-weight: 500;
}

.alertClose {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.alertClose:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Grid Layout */
.dashboardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Cards */
.card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.cardHeader {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.cardTitle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
}

.cardIcon {
  font-size: 1.4rem;
}

.cardContent {
  padding: 1.5rem;
}

/* Status Grid */
.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.statusItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.statusLabel {
  font-size: 0.9rem;
  color: #d1d5db;
  font-weight: 500;
}

.statusValue {
  font-weight: 600;
  color: #f3f4f6;
  font-size: 0.95rem;
}

.statusValue.enabled {
  color: #22c55e;
}

.statusValue.disabled {
  color: #ef4444;
}

.loadingStatus {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Export Options */
.exportOptions {
  display: grid;
  gap: 1rem;
}

.optionItem {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.optionItem:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.optionLabel {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  width: 100%;
}

.optionCheckbox {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  accent-color: #667eea;
  cursor: pointer;
}

.optionText {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.optionText strong {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

.optionText small {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Progress Bar */
.progressContainer {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progressLabel {
  margin-bottom: 1rem;
  font-weight: 600;
  color: #e5e7eb;
  text-align: center;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #22c55e, #16a34a);
  border-radius: 4px;
  transition: width 0.3s ease;
  animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.actionButton {
  flex: 1;
  min-width: 200px;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.primaryButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.primaryButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.successButton {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.successButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}

/* Stats Container */
.statsContainer {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.statsContainer h4 {
  margin-bottom: 1rem;
  color: #e5e7eb;
  font-size: 1.1rem;
  text-align: center;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.statLabel {
  font-size: 0.9rem;
  color: #d1d5db;
}

.statValue {
  font-weight: 600;
  color: #3b82f6;
  font-size: 1rem;
}

/* Preview Container */
.previewContainer {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.previewContainer h4 {
  margin-bottom: 1rem;
  color: #e5e7eb;
  font-size: 1.1rem;
}

.previewInfo {
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.previewInfo p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #d1d5db;
}

.previewInfo strong {
  color: #f3f4f6;
}

.previewContent {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #f0f0f0;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  line-height: 1.4;
}

/* Premium Features */
.premiumFeatures {
  display: grid;
  gap: 1.5rem;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.featureItem:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.featureIcon {
  font-size: 2rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.featureContent h4 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

.featureContent p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 1rem;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .dashboardGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .statusGrid {
    grid-template-columns: 1fr;
  }

  .statusItem {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .actionButtons {
    flex-direction: column;
  }

  .actionButton {
    min-width: auto;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .statItem {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
