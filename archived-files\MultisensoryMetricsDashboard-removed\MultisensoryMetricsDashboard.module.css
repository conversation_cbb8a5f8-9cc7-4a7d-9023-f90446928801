/**
 * @file MultisensoryMetricsDashboard.module.css
 * @description Estilos modulares para Dashboard de Métricas Multissensoriais
 * @version 3.0.0
 */

/* Container principal do dashboard */
.dashboardContainer {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Header do dashboard */
.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.dashboardTitle {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

/* Controles e filtros */
.dashboardControls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.timeframeSelector {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  font-size: 14px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeframeSelector:hover,
.timeframeSelector:focus {
  border-color: #4ecdc4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
  outline: none;
}

.refreshButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

/* Grid de métricas sensoriais */
.sensoryMetricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.sensoryMetricCard {
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.sensoryMetricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sensoryMetricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.sensoryMetricCard.visual::before {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.sensoryMetricCard.auditory::before {
  background: linear-gradient(90deg, #f093fb, #f5576c);
}

.sensoryMetricCard.tactile::before {
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.sensoryMetricCard.cognitive::before {
  background: linear-gradient(90deg, #ffecd2, #fcb69f);
}

.sensoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sensoryTitle {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin: 0;
}

.sensoryIcon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.sensoryIcon.visual {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.sensoryIcon.auditory {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.sensoryIcon.tactile {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.sensoryIcon.cognitive {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.sensoryValue {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 8px 0;
  line-height: 1;
}

.sensoryProgress {
  width: 100%;
  height: 8px;
  background-color: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 12px;
}

.sensoryProgressBar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.sensoryProgressBar.visual {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.sensoryProgressBar.auditory {
  background: linear-gradient(90deg, #f093fb, #f5576c);
}

.sensoryProgressBar.tactile {
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.sensoryProgressBar.cognitive {
  background: linear-gradient(90deg, #ffecd2, #fcb69f);
}

/* Grid de gráficos multissensoriais */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chartCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
}

.chartTitle {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chartContainer {
  position: relative;
  height: 300px;
}

.chartContainer.radar {
  height: 350px;
}

.chartContainer.doughnut {
  height: 280px;
}

/* Seção de insights multissensoriais */
.insightsSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.insightsTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.insightCard {
  background: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #4ecdc4;
  position: relative;
}

.insightCard.visual {
  border-left-color: #667eea;
}

.insightCard.auditory {
  border-left-color: #f093fb;
}

.insightCard.tactile {
  border-left-color: #4ecdc4;
}

.insightCard.cognitive {
  border-left-color: #ffecd2;
}

.insightTitle {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.insightContent {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

.insightScore {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #4ecdc4;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.insightScore.visual {
  background: #667eea;
}

.insightScore.auditory {
  background: #f093fb;
}

.insightScore.tactile {
  background: #4ecdc4;
}

.insightScore.cognitive {
  background: #fcb69f;
}

/* Filtros e opções */
.filtersSection {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.filtersTitle {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
}

.filtersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filterLabel {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #2d3748;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterSelect:hover,
.filterSelect:focus {
  border-color: #4ecdc4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
  outline: none;
}

/* Estados de loading e erro */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

.loadingText {
  color: #4a5568;
  font-size: 16px;
}

.errorContainer {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.emptyState {
  text-align: center;
  padding: 40px;
  color: #718096;
}

.emptyStateIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.emptyStateText {
  font-size: 18px;
  margin-bottom: 8px;
}

.emptyStateSubtext {
  font-size: 14px;
  opacity: 0.7;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 16px;
    margin-bottom: 16px;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .dashboardControls {
    justify-content: center;
  }

  .dashboardTitle {
    font-size: 24px;
    text-align: center;
  }

  .sensoryMetricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chartContainer {
    height: 250px;
  }

  .insightsGrid {
    grid-template-columns: 1fr;
  }

  .filtersGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .dashboardContainer {
    padding: 12px;
  }

  .dashboardTitle {
    font-size: 20px;
  }

  .sensoryValue {
    font-size: 28px;
  }

  .chartContainer {
    height: 200px;
  }

  .sensoryMetricsGrid {
    grid-template-columns: 1fr;
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.dashboardContainer {
  animation: fadeIn 0.5s ease-out;
}

.sensoryMetricCard {
  animation: slideIn 0.6s ease-out;
}

.chartCard,
.insightsSection {
  animation: fadeIn 0.6s ease-out;
}

/* Acessibilidade */
.refreshButton:focus-visible,
.timeframeSelector:focus-visible,
.filterSelect:focus-visible {
  outline: 2px solid #4ecdc4;
  outline-offset: 2px;
}

/* Tema escuro (futuro) */
@media (prefers-color-scheme: dark) {
  .dashboardContainer {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  .chartCard,
  .insightsSection,
  .filtersSection {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .sensoryMetricCard {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
  }

  .dashboardTitle,
  .chartTitle,
  .insightsTitle,
  .sensoryValue {
    color: #e2e8f0;
  }

  .insightCard {
    background-color: #4a5568;
  }

  .sensoryProgress {
    background-color: #4a5568;
  }
}

/* AI Analysis Section */
.aiAnalysisSection {
  margin-top: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.aiAnalysisTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.aiLoading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  color: white;
  font-size: 1.1rem;
}

.aiInsightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.aiInsightCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  color: #2d3748;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.aiInsightHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #4ecdc4;
}

.aiInsightIcon {
  font-size: 1.5rem;
}

.aiInsightHeader h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.aiInsightList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.aiInsightItem {
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  line-height: 1.5;
  position: relative;
  padding-left: 1.5rem;
}

.aiInsightItem:last-child {
  border-bottom: none;
}

.aiInsightItem::before {
  content: '•';
  color: #4ecdc4;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0.75rem;
}

.modalityProfile {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modalityPrimary,
.modalitySecondary,
.modalityStrength {
  padding: 0.5rem 0;
  color: #4a5568;
}

.modalityPrimary strong,
.modalitySecondary strong,
.modalityStrength strong {
  color: #2d3748;
}

/* Responsividade para AI Analysis */
@media (max-width: 768px) {
  .aiAnalysisSection {
    margin-top: 2rem;
    padding: 1.5rem;
  }

  .aiAnalysisTitle {
    font-size: 1.5rem;
  }

  .aiInsightsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .aiInsightCard {
    padding: 1rem;
  }
}
