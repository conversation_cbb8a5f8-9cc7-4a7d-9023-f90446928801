/**
 * @file BackupExportDashboard.jsx
 * @description Dashboard Premium de Backup e Exportação de Dados
 * @version 3.1.0
 */

import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import styles from './BackupExportDashboard.module.css'

function BackupExportDashboard({ 
  userId = 'user_demo', 
  userDetails = null, 
  isPremiumUser = true,
  onError = () => {},
  onLoading = () => {}
}) {
  const [backupData, setBackupData] = useState(null)
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [alert, setAlert] = useState(null)
  const [importFile, setImportFile] = useState(null)
  const [importPreview, setImportPreview] = useState(null)
  const [backupStats, setBackupStats] = useState(null)
  const [exportProgress, setExportProgress] = useState(0)
  const [backupStatus, setBackupStatus] = useState(null)
  const [exportOptions, setExportOptions] = useState({
    userProfiles: true,
    gameProgress: true,
    accessibilitySettings: true,
    preferences: true,
    gameMetrics: true,
    sessionData: true,
  })

  // Buscar status de backup ao carregar o componente
  useEffect(() => {
    const fetchBackupStatus = async () => {
      if (userId) {
        try {
          onLoading(true)
          const response = await fetch(`/api/backup/status/${userId}`)
          if (response.ok) {
            const data = await response.json()
            setBackupStatus(data.data)
          }
        } catch (error) {
          console.warn('Erro ao buscar status de backup:', error)
        } finally {
          onLoading(false)
        }
      }
    }

    fetchBackupStatus()
  }, [userId, onLoading])

  // Função para mostrar alertas temporários
  const showAlert = (type, message) => {
    setAlert({ type, message })
    setTimeout(() => setAlert(null), 5000)
  }

  // Função para gerar o backup dos dados (melhorada)
  const generateBackup = async () => {
    if (!userId) {
      showAlert('error', 'Erro: Não foi possível identificar o usuário.')
      return
    }

    setIsExporting(true)
    setExportProgress(0)
    onLoading(true)

    try {
      let backupData = {
        version: '3.1.0',
        exportDate: new Date().toISOString(),
        userId: userId,
        userDetails: userDetails,
        data: {},
        metadata: {
          source: 'premium_dashboard',
          totalItems: 0,
          categories: []
        }
      }

      let totalItems = 0
      const categories = []

      // Progresso: 10%
      setExportProgress(10)

      // Backup de perfis de usuário
      if (exportOptions.userProfiles) {
        const profiles = localStorage.getItem('betina_profiles')
        if (profiles) {
          try {
            backupData.data.userProfiles = JSON.parse(profiles)
            totalItems += Array.isArray(backupData.data.userProfiles) ? backupData.data.userProfiles.length : 1
            categories.push('userProfiles')
          } catch (e) {
            backupData.data.userProfiles = profiles
          }
        }
      }

      // Progresso: 25%
      setExportProgress(25)

      // Backup de progresso dos jogos (melhorado)
      if (exportOptions.gameProgress) {
        const gameData = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key.startsWith('betina_') && (key.includes('_history') || key.includes('_progress') || key.includes('_scores'))) {
            try {
              gameData[key] = JSON.parse(localStorage.getItem(key))
              totalItems++
            } catch (e) {
              gameData[key] = localStorage.getItem(key)
            }
          }
        }
        backupData.data.gameProgress = gameData
        if (Object.keys(gameData).length > 0) categories.push('gameProgress')
      }

      // Progresso: 50%
      setExportProgress(50)

      // Backup de métricas de jogos
      if (exportOptions.gameMetrics) {
        const metricsData = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key.startsWith('betina_') && key.includes('_metrics')) {
            try {
              metricsData[key] = JSON.parse(localStorage.getItem(key))
              totalItems++
            } catch (e) {
              metricsData[key] = localStorage.getItem(key)
            }
          }
        }
        backupData.data.gameMetrics = metricsData
        if (Object.keys(metricsData).length > 0) categories.push('gameMetrics')
      }

      // Progresso: 75%
      setExportProgress(75)

      // Backup de configurações de acessibilidade
      if (exportOptions.accessibilitySettings) {
        const accessSettings = localStorage.getItem('betina_accessibility_settings')
        if (accessSettings) {
          try {
            backupData.data.accessibilitySettings = JSON.parse(accessSettings)
            totalItems++
            categories.push('accessibilitySettings')
          } catch (e) {
            backupData.data.accessibilitySettings = accessSettings
          }
        }
      }

      // Backup de preferências
      if (exportOptions.preferences) {
        const preferences = localStorage.getItem('betina_user_preferences')
        if (preferences) {
          try {
            backupData.data.preferences = JSON.parse(preferences)
            totalItems++
            categories.push('preferences')
          } catch (e) {
            backupData.data.preferences = preferences
          }
        }
      }

      // Backup de dados de sessão
      if (exportOptions.sessionData) {
        const sessionData = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key.startsWith('betina_') && key.includes('_session')) {
            try {
              sessionData[key] = JSON.parse(localStorage.getItem(key))
              totalItems++
            } catch (e) {
              sessionData[key] = localStorage.getItem(key)
            }
          }
        }
        backupData.data.sessionData = sessionData
        if (Object.keys(sessionData).length > 0) categories.push('sessionData')
      }

      // Progresso: 90%
      setExportProgress(90)

      // Se usuário premium, tentar buscar dados adicionais do servidor
      if (isPremiumUser) {
        try {
          const response = await fetch('/api/backup/user-data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              userId, 
              options: exportOptions 
            })
          })

          if (response.ok) {
            const serverData = await response.json()
            backupData.data.serverData = serverData.data
            totalItems += serverData.totalItems || 0
            categories.push('serverData')
          }
        } catch (error) {
          console.warn('Não foi possível obter dados do servidor:', error)
        }
      }

      // Finalizar metadados
      backupData.metadata.totalItems = totalItems
      backupData.metadata.categories = categories

      // Progresso: 100%
      setExportProgress(100)

      // Calcular estatísticas do backup
      const stats = {
        totalSize: JSON.stringify(backupData).length,
        totalItems: totalItems,
        categories: categories.length,
        compressionRatio: 0.85, // Estimativa
        estimatedDownloadTime: Math.ceil(JSON.stringify(backupData).length / 1024 / 100) // segundos estimados
      }

      setBackupData(backupData)
      setBackupStats(stats)
      showAlert('success', `Backup gerado com sucesso! ${totalItems} itens em ${categories.length} categorias.`)
    } catch (error) {
      console.error('Erro ao gerar backup:', error)
      onError(`Erro ao gerar backup: ${error.message}`)
      showAlert('error', `Erro ao gerar backup: ${error.message}`)
    } finally {
      setIsExporting(false)
      setExportProgress(0)
      onLoading(false)
    }
  }

  // Função para fazer download do backup (melhorada)
  const downloadBackup = () => {
    if (!backupData) return

    const dataStr = JSON.stringify(backupData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
    const filename = `betina-premium-backup-${userId}-${timestamp}.json`

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = filename
    link.click()

    URL.revokeObjectURL(link.href)

    // Log do download para estatísticas
    console.log(`Backup Premium baixado: ${filename}, Tamanho: ${(dataBlob.size / 1024).toFixed(2)} KB`)
    
    showAlert('success', `Backup baixado com sucesso! Arquivo: ${filename}`)
  }

  // Função para alterar opções de exportação
  const handleExportOptionChange = (option) => {
    setExportOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }))
  }

  return (
    <div className={styles.dashboardContainer}>
      {/* Header do Dashboard */}
      <div className={styles.dashboardHeader}>
        <div className={styles.headerContent}>
          <h1 className={styles.title}>
            <span className={styles.titleIcon}>💾</span>
            Backup e Exportação Premium
          </h1>
          <p className={styles.subtitle}>
            Gerencie seus dados com recursos avançados exclusivos para usuários premium
          </p>
        </div>
        
        {isPremiumUser && (
          <div className={styles.premiumBadge}>
            <span className={styles.premiumIcon}>💎</span>
            Premium
          </div>
        )}
      </div>

      {/* Alertas */}
      {alert && (
        <div className={`${styles.alert} ${styles[alert.type]}`}>
          <span className={styles.alertIcon}>
            {alert.type === 'success' ? '✅' : alert.type === 'error' ? '❌' : 'ℹ️'}
          </span>
          <span className={styles.alertMessage}>{alert.message}</span>
          <button 
            className={styles.alertClose}
            onClick={() => setAlert(null)}
          >
            ×
          </button>
        </div>
      )}

      <div className={styles.dashboardGrid}>
        {/* Seção: Status de Backup */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>
              <span className={styles.cardIcon}>📊</span>
              Status de Backup
            </h2>
          </div>
          <div className={styles.cardContent}>
            {backupStatus ? (
              <div className={styles.statusGrid}>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Último backup:</span>
                  <span className={styles.statusValue}>
                    {new Date(backupStatus.lastBackup).toLocaleString('pt-BR')}
                  </span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Total de backups:</span>
                  <span className={styles.statusValue}>{backupStatus.totalBackups}</span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Tamanho total:</span>
                  <span className={styles.statusValue}>
                    {(backupStatus.totalDataSize / 1024 / 1024).toFixed(2)} MB
                  </span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Backup automático:</span>
                  <span className={`${styles.statusValue} ${backupStatus.autoBackupEnabled ? styles.enabled : styles.disabled}`}>
                    {backupStatus.autoBackupEnabled ? 'Ativado' : 'Desativado'}
                  </span>
                </div>
              </div>
            ) : (
              <div className={styles.loadingStatus}>
                <div className={styles.loadingSpinner}></div>
                <p>Carregando informações de backup...</p>
              </div>
            )}
          </div>
        </div>

        {/* Seção: Opções de Exportação */}
        <div className={styles.card}>
          <div className={styles.cardHeader}>
            <h2 className={styles.cardTitle}>
              <span className={styles.cardIcon}>⚙️</span>
              Opções de Exportação
            </h2>
          </div>
          <div className={styles.cardContent}>
            <div className={styles.exportOptions}>
              {Object.entries({
                userProfiles: { icon: '👤', title: 'Perfis de usuário', desc: 'Informações dos perfis criados e configurações pessoais' },
                gameProgress: { icon: '🎮', title: 'Progresso nos jogos', desc: 'Histórico, pontuações e progressão em todos os jogos' },
                gameMetrics: { icon: '📊', title: 'Métricas de jogos', desc: 'Dados detalhados de performance e análises' },
                sessionData: { icon: '🕐', title: 'Dados de sessão', desc: 'Informações de sessões terapêuticas e atividades' },
                accessibilitySettings: { icon: '♿', title: 'Configurações de acessibilidade', desc: 'Preferências de alto contraste, fonte, navegação, etc.' },
                preferences: { icon: '⚙️', title: 'Preferências gerais', desc: 'Configurações personalizadas do sistema e interface' }
              }).map(([key, option]) => (
                <div key={key} className={styles.optionItem}>
                  <label className={styles.optionLabel}>
                    <input
                      type="checkbox"
                      checked={exportOptions[key]}
                      onChange={() => handleExportOptionChange(key)}
                      className={styles.optionCheckbox}
                    />
                    <span className={styles.optionText}>
                      <strong>{option.icon} {option.title}</strong>
                      <small>{option.desc}</small>
                    </span>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Seção: Ações de Backup */}
      <div className={styles.card}>
        <div className={styles.cardHeader}>
          <h2 className={styles.cardTitle}>
            <span className={styles.cardIcon}>🚀</span>
            Ações de Backup
          </h2>
        </div>
        <div className={styles.cardContent}>
          {/* Barra de progresso */}
          {isExporting && (
            <div className={styles.progressContainer}>
              <div className={styles.progressLabel}>
                Gerando backup... {exportProgress}%
              </div>
              <div className={styles.progressBar}>
                <div
                  className={styles.progressFill}
                  style={{ width: `${exportProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className={styles.actionButtons}>
            <button
              onClick={generateBackup}
              disabled={isExporting || !userId}
              className={`${styles.actionButton} ${styles.primaryButton}`}
            >
              {isExporting ? '🔄 Gerando...' : '📦 Gerar Backup Premium'}
            </button>

            {backupData && (
              <button
                onClick={downloadBackup}
                className={`${styles.actionButton} ${styles.successButton}`}
              >
                💾 Baixar Backup
              </button>
            )}
          </div>

          {/* Estatísticas do backup */}
          {backupStats && (
            <div className={styles.statsContainer}>
              <h4>📊 Estatísticas do Backup:</h4>
              <div className={styles.statsGrid}>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Total de itens:</span>
                  <span className={styles.statValue}>{backupStats.totalItems}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Categorias:</span>
                  <span className={styles.statValue}>{backupStats.categories}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Tamanho:</span>
                  <span className={styles.statValue}>{(backupStats.totalSize / 1024).toFixed(2)} KB</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Tempo estimado:</span>
                  <span className={styles.statValue}>{backupStats.estimatedDownloadTime}s</span>
                </div>
              </div>
            </div>
          )}

          {backupData && (
            <div className={styles.previewContainer}>
              <h4>📋 Preview do Backup:</h4>
              <div className={styles.previewInfo}>
                <p><strong>Versão:</strong> {backupData.version}</p>
                <p><strong>Data:</strong> {new Date(backupData.exportDate).toLocaleString('pt-BR')}</p>
                <p><strong>Usuário:</strong> {backupData.userId}</p>
                <p><strong>Fonte:</strong> {backupData.metadata?.source || 'premium_dashboard'}</p>
                <p><strong>Categorias:</strong> {backupData.metadata?.categories?.join(', ') || 'N/A'}</p>
              </div>
              <pre className={styles.previewContent}>
                <code>{JSON.stringify(backupData, null, 2).substring(0, 800)}...</code>
              </pre>
            </div>
          )}
        </div>
      </div>

      {/* Recursos Premium */}
      <div className={styles.card}>
        <div className={styles.cardHeader}>
          <h2 className={styles.cardTitle}>
            <span className={styles.cardIcon}>💎</span>
            Recursos Premium
          </h2>
        </div>
        <div className={styles.cardContent}>
          <div className={styles.premiumFeatures}>
            <div className={styles.featureItem}>
              <span className={styles.featureIcon}>🔄</span>
              <div className={styles.featureContent}>
                <h4>Backup Automático</h4>
                <p>Backups automáticos agendados para garantir que seus dados estejam sempre seguros</p>
              </div>
            </div>
            <div className={styles.featureItem}>
              <span className={styles.featureIcon}>☁️</span>
              <div className={styles.featureContent}>
                <h4>Sincronização na Nuvem</h4>
                <p>Seus backups são armazenados com segurança na nuvem e sincronizados entre dispositivos</p>
              </div>
            </div>
            <div className={styles.featureItem}>
              <span className={styles.featureIcon}>🔐</span>
              <div className={styles.featureContent}>
                <h4>Criptografia Avançada</h4>
                <p>Todos os backups são criptografados com algoritmos de segurança de nível militar</p>
              </div>
            </div>
            <div className={styles.featureItem}>
              <span className={styles.featureIcon}>📈</span>
              <div className={styles.featureContent}>
                <h4>Análise Avançada</h4>
                <p>Relatórios detalhados sobre seus dados e padrões de uso ao longo do tempo</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

BackupExportDashboard.propTypes = {
  userId: PropTypes.string,
  userDetails: PropTypes.object,
  isPremiumUser: PropTypes.bool,
  onError: PropTypes.func,
  onLoading: PropTypes.func
}

export default BackupExportDashboard
