/**
 * @file user-backup.js
 * @description API para backup e exportação de dados do usuário
 * @version 3.1.0
 */

import express from 'express'

const router = express.Router()

// Logger simples para backup
const logger = {
  info: (message, data = {}) => console.log(`[BACKUP-API] ${message}`, data),
  warn: (message, data = {}) => console.warn(`[BACKUP-API] ${message}`, data),
  error: (message, data = {}) => console.error(`[BACKUP-API] ${message}`, data)
}

// Funções auxiliares para cálculos de métricas reais
function calculateImprovementTrend(sessions) {
  if (sessions.length < 2) return 0
  
  const sortedSessions = sessions.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
  const recentSessions = sortedSessions.slice(-5) // Últimas 5 sessões
  
  if (recentSessions.length < 2) return 0
  
  const firstAvg = recentSessions.slice(0, Math.ceil(recentSessions.length / 2))
    .reduce((acc, s) => acc + (s.score || 0), 0) / Math.ceil(recentSessions.length / 2)
  
  const lastAvg = recentSessions.slice(Math.floor(recentSessions.length / 2))
    .reduce((acc, s) => acc + (s.score || 0), 0) / Math.floor(recentSessions.length / 2)
  
  return Math.round(((lastAvg - firstAvg) / firstAvg) * 100) || 0
}

function calculateConsistencyScore(sessions) {
  if (sessions.length < 3) return 50 // Score neutro para poucos dados
  
  const scores = sessions.map(s => s.score || 0).filter(s => s > 0)
  if (scores.length === 0) return 0
  
  const mean = scores.reduce((a, b) => a + b, 0) / scores.length
  const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length
  const standardDeviation = Math.sqrt(variance)
  
  // Consistência é inversamente proporcional ao desvio padrão
  const consistency = Math.max(0, 100 - (standardDeviation / mean) * 100)
  return Math.round(consistency)
}

function calculateEngagementLevel(sessions) {
  if (sessions.length === 0) return 0
  
  const now = new Date()
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  
  const recentSessions = sessions.filter(s => new Date(s.timestamp) >= oneWeekAgo)
  const averageSessionLength = sessions.reduce((acc, s) => acc + (s.duration || 0), 0) / sessions.length
  
  // Engajamento baseado em frequência e duração das sessões
  const frequencyScore = Math.min(100, (recentSessions.length / 7) * 100) // Ideal: 1 por dia
  const durationScore = Math.min(100, (averageSessionLength / 300000) * 100) // Ideal: 5min
  
  return Math.round((frequencyScore + durationScore) / 2)
}

/**
 * GET /api/backup/
 * Lista todos os endpoints disponíveis da API de backup
 */
router.get('/', (req, res) => {
  logger.info('Documentação da API acessada')
  res.json({
    success: true,
    message: 'API de Backup e Exportação - Portal Betina V3',
    version: '3.1.0',
    endpoints: {
      'GET /api/backup/': {
        description: 'Lista todos os endpoints disponíveis',
        parameters: 'Nenhum',
        response: 'Documentação da API'
      },
      'GET /api/backup/test': {
        description: 'Testa se a API está funcionando',
        parameters: 'Nenhum',
        response: 'Status da API'
      },
      'GET /api/backup/status/:userId': {
        description: 'Busca status de backup do usuário',
        parameters: 'userId (string) - ID do usuário',
        response: 'Status detalhado dos backups'
      },
      'GET /api/backup/history/:userId': {
        description: 'Busca histórico de backups do usuário',
        parameters: 'userId (string), limit (number), offset (number)',
        response: 'Lista de backups anteriores'
      },
      'POST /api/backup/user-data': {
        description: 'Gera backup dos dados do usuário',
        parameters: 'userId (string), options (object)',
        response: 'Dados do backup gerado'
      },
      'POST /api/backup/import': {
        description: 'Importa dados de backup',
        parameters: 'userId (string), backupData (object), options (object)',
        response: 'Resultado da importação'
      },
      'POST /api/backup/validate': {
        description: 'Valida estrutura de um backup',
        parameters: 'backupData (object)',
        response: 'Resultado da validação'
      },
      'DELETE /api/backup/:userId/:backupId': {
        description: 'Remove um backup específico',
        parameters: 'userId (string), backupId (string)',
        response: 'Confirmação da remoção'
      }
    },
    examples: {
      backup_options: {
        userProfiles: true,
        gameProgress: true,
        gameMetrics: true,
        sessionData: true,
        accessibilitySettings: true,
        preferences: true
      }
    },
    timestamp: new Date().toISOString()
  })
})

/**
 * GET /api/backup/test
 * Rota de teste para verificar se a API está funcionando
 */
router.get('/test', (req, res) => {
  logger.info('Rota de teste acessada')
  res.json({
    success: true,
    message: 'API de backup funcionando!',
    timestamp: new Date().toISOString()
  })
})

/**
 * POST /api/backup/user-data
 * Gera backup completo dos dados do usuário
 */
router.post('/user-data', async (req, res) => {
    try {
      const { userId, options = {} } = req.body

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: 'userId é obrigatório'
        })
      }

      logger.info('Iniciando backup de dados do usuário', {
        userId,
        options,
        timestamp: new Date().toISOString()
      })

      const backupData = {
        version: '3.1.0',
        exportDate: new Date().toISOString(),
        userId: userId,
        data: {},
        metadata: {
          source: 'database',
          totalItems: 0,
          categories: []
        }
      }

      let totalItems = 0
      const categories = []

      // Backup de perfis de usuário
      if (options.userProfiles) {
        try {
          // Buscar dados reais dos perfis do usuário
          const profilesData = {}
          
          // Coletar dados reais do localStorage do usuário
          const storedProfiles = JSON.parse(global.localStorage?.getItem('betina_profiles') || '[]')
          const userPreferences = JSON.parse(global.localStorage?.getItem('betina_user_preferences') || '{}')
          const userSettings = JSON.parse(global.localStorage?.getItem('betina_user_settings') || '{}')
          
          // Se não há dados locais, buscar do histórico de sessões
          if (storedProfiles.length === 0) {
            // Dados baseados na atividade real do usuário
            const realUserData = {
              id: userId,
              name: userSettings.userName || 'Usuário',
              email: userSettings.userEmail || `${userId}@portalbetina.com`,
              preferences: userPreferences,
              settings: userSettings,
              profilesHistory: storedProfiles,
              lastLogin: new Date().toISOString(),
              createdAt: userSettings.createdAt || new Date().toISOString()
            }
            profilesData.mainProfile = realUserData
          } else {
            profilesData.profiles = storedProfiles
            profilesData.preferences = userPreferences
            profilesData.settings = userSettings
          }
          
          backupData.data.userProfiles = profilesData
          totalItems += Object.keys(profilesData).length
          categories.push('userProfiles')
        } catch (error) {
          logger.warn('Erro ao buscar perfis de usuário', { error: error.message })
        }
      }

      // Backup de métricas de jogos
      if (options.gameMetrics) {
        try {
          // Buscar métricas reais dos jogos
          const realGameMetrics = {}
          
          // Buscar dados reais do localStorage
          if (global.localStorage) {
            // Coletar todas as métricas salvas
            for (let i = 0; i < global.localStorage.length; i++) {
              const key = global.localStorage.key(i)
              if (key && key.includes('_metrics') || key.includes('_scores') || key.includes('gameScores')) {
                try {
                  const data = JSON.parse(global.localStorage.getItem(key))
                  realGameMetrics[key] = data
                } catch (e) {
                  realGameMetrics[key] = global.localStorage.getItem(key)
                }
              }
            }
            
            // Processar métricas específicas por jogo
            const gameScores = JSON.parse(global.localStorage.getItem('gameScores') || '[]')
            const gameMetrics = JSON.parse(global.localStorage.getItem('gameMetrics') || '[]')
            
            if (gameScores.length > 0 || gameMetrics.length > 0) {
              // Calcular métricas reais baseadas nos dados coletados
              const processedMetrics = {}
              
              gameScores.forEach(score => {
                if (!processedMetrics[score.gameId]) {
                  processedMetrics[score.gameId] = {
                    sessions: 0,
                    totalScore: 0,
                    bestScore: 0,
                    avgScore: 0,
                    totalTime: 0,
                    avgTime: 0,
                    lastPlayed: null,
                    accuracy: []
                  }
                }
                
                const game = processedMetrics[score.gameId]
                game.sessions++
                game.totalScore += score.score || 0
                game.bestScore = Math.max(game.bestScore, score.score || 0)
                game.totalTime += score.timeSpent || 0
                game.lastPlayed = score.timestamp
                if (score.accuracy) game.accuracy.push(score.accuracy)
              })
              
              // Calcular médias reais
              Object.keys(processedMetrics).forEach(gameId => {
                const game = processedMetrics[gameId]
                game.avgScore = game.sessions > 0 ? Math.round(game.totalScore / game.sessions) : 0
                game.avgTime = game.sessions > 0 ? Math.round(game.totalTime / game.sessions) : 0
                game.avgAccuracy = game.accuracy.length > 0 ? 
                  Math.round(game.accuracy.reduce((a, b) => a + b, 0) / game.accuracy.length) : 0
                delete game.accuracy // Remove array temporário
              })
              
              realGameMetrics.processedMetrics = processedMetrics
            }
          }
          
          // Se não há dados reais, usar dados padrão mais realistas
          if (Object.keys(realGameMetrics).length === 0) {
            realGameMetrics.noDataAvailable = {
              message: 'Nenhuma métrica encontrada - usuário novo',
              timestamp: new Date().toISOString(),
              defaultMetrics: {
                totalGamesPlayed: 0,
                totalSessions: 0,
                totalPlayTime: 0
              }
            }
          }
          
          backupData.data.gameMetrics = realGameMetrics
          totalItems += Object.keys(realGameMetrics).length
          categories.push('gameMetrics')
        } catch (error) {
          logger.warn('Erro ao buscar métricas de jogos', { error: error.message })
        }
      }

      // Backup de dados de sessão
      if (options.sessionData) {
        try {
          // Buscar dados reais de sessão
          const realSessionData = {}
          
          if (global.localStorage) {
            // Coletar dados de sessão reais
            const gameSessions = JSON.parse(global.localStorage.getItem('gameSessions') || '[]')
            const userProgress = JSON.parse(global.localStorage.getItem('userProgress') || '{}')
            const sessionHistory = JSON.parse(global.localStorage.getItem('betina_session_history') || '[]')
            
            // Calcular estatísticas reais de sessão
            if (gameSessions.length > 0) {
              const totalSessions = gameSessions.length
              const totalPlayTime = gameSessions.reduce((acc, session) => acc + (session.duration || 0), 0)
              const averageSessionDuration = totalSessions > 0 ? Math.round(totalPlayTime / totalSessions) : 0
              
              // Encontrar última sessão
              const lastSession = gameSessions.reduce((latest, session) => {
                return new Date(session.timestamp) > new Date(latest.timestamp) ? session : latest
              }, gameSessions[0])
              
              // Extrair objetivos terapêuticos baseados nos jogos jogados
              const gamesPlayed = [...new Set(gameSessions.map(s => s.gameId))]
              const therapeuticGoals = []
              
              gamesPlayed.forEach(gameId => {
                switch (gameId) {
                  case 'memory-game':
                  case 'MemoryGame':
                    therapeuticGoals.push('memory', 'concentration')
                    break
                  case 'color-match':
                  case 'ColorMatch':
                    therapeuticGoals.push('attention', 'visual_processing')
                    break
                  case 'quebra-cabeca':
                  case 'QuebraCabeca':
                    therapeuticGoals.push('problem_solving', 'spatial_reasoning')
                    break
                  case 'letter-recognition':
                    therapeuticGoals.push('language', 'recognition')
                    break
                  case 'musical-sequence':
                    therapeuticGoals.push('auditory_processing', 'sequence')
                    break
                }
              })
              
              realSessionData = {
                totalSessions,
                totalPlayTime,
                averageSessionDuration,
                lastSession: lastSession.timestamp,
                therapeuticGoals: [...new Set(therapeuticGoals)], // Remove duplicatas
                gamesPlayed,
                sessionHistory: gameSessions,
                userProgress,
                performance: {
                  improvementTrend: calculateImprovementTrend(gameSessions),
                  consistencyScore: calculateConsistencyScore(gameSessions),
                  engagementLevel: calculateEngagementLevel(gameSessions)
                }
              }
            } else {
              realSessionData = {
                totalSessions: 0,
                totalPlayTime: 0,
                averageSessionDuration: 0,
                lastSession: null,
                therapeuticGoals: [],
                gamesPlayed: [],
                sessionHistory: [],
                userProgress: {},
                message: 'Nenhuma sessão encontrada - usuário novo ou dados não disponíveis'
              }
            }
          }
          
          backupData.data.sessionData = realSessionData
          totalItems++
          categories.push('sessionData')
        } catch (error) {
          logger.warn('Erro ao buscar dados de sessão', { error: error.message })
        }
      }

      // Backup de progresso nos jogos
      if (options.gameProgress) {
        try {
          // Buscar progresso real nos jogos
          const realGameProgress = {}
          
          if (global.localStorage) {
            // Coletar dados de progresso reais
            for (let i = 0; i < global.localStorage.length; i++) {
              const key = global.localStorage.key(i)
              if (key && (key.includes('_progress') || key.includes('_level') || key.includes('_achievements'))) {
                try {
                  const data = JSON.parse(global.localStorage.getItem(key))
                  realGameProgress[key] = data
                } catch (e) {
                  realGameProgress[key] = global.localStorage.getItem(key)
                }
              }
            }
            
            // Processar progresso por jogo específico
            const gameScores = JSON.parse(global.localStorage.getItem('gameScores') || '[]')
            const userProgress = JSON.parse(global.localStorage.getItem('userProgress') || '{}')
            
            // Calcular progresso real baseado nos scores
            const progressByGame = {}
            gameScores.forEach(score => {
              if (!progressByGame[score.gameId]) {
                progressByGame[score.gameId] = {
                  level: 1,
                  completed: false,
                  achievements: [],
                  bestScore: 0,
                  totalAttempts: 0,
                  successRate: 0,
                  progression: []
                }
              }
              
              const game = progressByGame[score.gameId]
              game.totalAttempts++
              game.bestScore = Math.max(game.bestScore, score.score || 0)
              game.progression.push({
                score: score.score,
                accuracy: score.accuracy,
                timestamp: score.timestamp
              })
              
              // Calcular nível baseado no desempenho
              if (score.accuracy >= 90) {
                game.level = Math.min(game.level + 1, 10)
                if (!game.achievements.includes('high_accuracy')) {
                  game.achievements.push('high_accuracy')
                }
              }
              
              if (score.score >= 100) {
                if (!game.achievements.includes('perfect_score')) {
                  game.achievements.push('perfect_score')
                }
              }
              
              // Marcar como completo se nível alto
              if (game.level >= 5) {
                game.completed = true
                if (!game.achievements.includes('level_master')) {
                  game.achievements.push('level_master')
                }
              }
            })
            
            // Calcular taxa de sucesso
            Object.keys(progressByGame).forEach(gameId => {
              const game = progressByGame[gameId]
              const successfulAttempts = game.progression.filter(p => p.accuracy >= 70).length
              game.successRate = game.totalAttempts > 0 ? 
                Math.round((successfulAttempts / game.totalAttempts) * 100) : 0
            })
            
            realGameProgress.progressByGame = progressByGame
            realGameProgress.userProgress = userProgress
          }
          
          // Se não há dados, indicar usuário novo
          if (Object.keys(realGameProgress).length === 0) {
            realGameProgress = {
              message: 'Nenhum progresso encontrado - usuário novo',
              defaultProgress: {
                totalGamesStarted: 0,
                totalGamesCompleted: 0,
                overallLevel: 1,
                achievements: []
              }
            }
          }
          
          backupData.data.gameProgress = realGameProgress
          totalItems += Object.keys(realGameProgress).length
          categories.push('gameProgress')
        } catch (error) {
          logger.warn('Erro ao buscar progresso dos jogos', { error: error.message })
        }
      }

      // Finalizar metadados
      backupData.metadata.totalItems = totalItems
      backupData.metadata.categories = categories

      logger.info('Backup gerado com sucesso', {
        userId,
        totalItems,
        categories: categories.length,
        dataSize: JSON.stringify(backupData).length
      })

      // Salvar registro do backup no histórico
      if (global.localStorage) {
        try {
          const backupRecord = {
            id: `backup_${Date.now()}`,
            date: new Date().toISOString(),
            size: JSON.stringify(backupData).length,
            categories: categories,
            success: true,
            version: '3.1.0',
            totalItems: totalItems
          }
          
          // Recuperar histórico existente
          const existingHistory = JSON.parse(global.localStorage.getItem(`betina_backup_history_${userId}`) || '[]')
          existingHistory.push(backupRecord)
          
          // Manter apenas os últimos 50 backups
          if (existingHistory.length > 50) {
            existingHistory.splice(0, existingHistory.length - 50)
          }
          
          global.localStorage.setItem(`betina_backup_history_${userId}`, JSON.stringify(existingHistory))
          global.localStorage.setItem(`betina_last_backup_${userId}`, JSON.stringify({
            timestamp: new Date().toISOString(),
            size: backupRecord.size,
            categories: categories
          }))
          
        } catch (error) {
          logger.warn('Erro ao salvar histórico de backup', { error: error.message })
        }
      }

      res.json({
        success: true,
        message: 'Backup gerado com sucesso',
        data: backupData.data,
        totalItems: totalItems,
        categories: categories,
        metadata: backupData.metadata
      })

    } catch (error) {
      logger.error('Erro ao gerar backup', {
        error: error.message,
        stack: error.stack,
        userId: req.body.userId
      })

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor ao gerar backup',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  })

/**
 * POST /api/backup/import
 * Importa dados de backup para o usuário
 */
router.post('/import', async (req, res) => {
    try {
      const { userId, backupData, options = {} } = req.body

      if (!userId || !backupData) {
        return res.status(400).json({
          success: false,
          message: 'userId e backupData são obrigatórios'
        })
      }

      logger.info('Iniciando importação de backup', {
        userId,
        backupVersion: backupData.version,
        categories: backupData.metadata?.categories || []
      })

      const importResults = {
        imported: [],
        skipped: [],
        errors: []
      }

      // Validar versão do backup
      if (!backupData.version || backupData.version < '3.0.0') {
        return res.status(400).json({
          success: false,
          message: 'Versão do backup não suportada. Versão mínima: 3.0.0'
        })
      }

      // Importar dados conforme opções
      if (options.userProfiles && backupData.data.userProfiles) {
        try {
          // Salvar perfis reais no sistema
          if (global.localStorage) {
            global.localStorage.setItem('betina_profiles', JSON.stringify(backupData.data.userProfiles))
            
            // Se há preferências, salvar separadamente
            if (backupData.data.userProfiles.preferences) {
              global.localStorage.setItem('betina_user_preferences', JSON.stringify(backupData.data.userProfiles.preferences))
            }
            
            // Se há configurações, salvar
            if (backupData.data.userProfiles.settings) {
              global.localStorage.setItem('betina_user_settings', JSON.stringify(backupData.data.userProfiles.settings))
            }
          }
          
          logger.info('Perfis de usuário importados com sucesso', { userId })
          importResults.imported.push('userProfiles')
        } catch (error) {
          importResults.errors.push({ category: 'userProfiles', error: error.message })
        }
      }

      if (options.gameMetrics && backupData.data.gameMetrics) {
        try {
          // Salvar métricas reais no sistema
          if (global.localStorage) {
            // Salvar métricas processadas
            if (backupData.data.gameMetrics.processedMetrics) {
              global.localStorage.setItem('gameMetrics', JSON.stringify(Object.values(backupData.data.gameMetrics.processedMetrics)))
            }
            
            // Salvar dados brutos de métricas
            Object.keys(backupData.data.gameMetrics).forEach(key => {
              if (key.includes('_metrics') || key.includes('_scores')) {
                global.localStorage.setItem(key, JSON.stringify(backupData.data.gameMetrics[key]))
              }
            })
          }
          
          logger.info('Métricas de jogos importadas com sucesso', { userId })
          importResults.imported.push('gameMetrics')
        } catch (error) {
          importResults.errors.push({ category: 'gameMetrics', error: error.message })
        }
      }

      if (options.sessionData && backupData.data.sessionData) {
        try {
          // Salvar dados de sessão reais
          if (global.localStorage) {
            if (backupData.data.sessionData.sessionHistory) {
              global.localStorage.setItem('gameSessions', JSON.stringify(backupData.data.sessionData.sessionHistory))
            }
            
            if (backupData.data.sessionData.userProgress) {
              global.localStorage.setItem('userProgress', JSON.stringify(backupData.data.sessionData.userProgress))
            }
            
            // Salvar histórico de sessões
            global.localStorage.setItem('betina_session_history', JSON.stringify(backupData.data.sessionData.sessionHistory || []))
          }
          
          logger.info('Dados de sessão importados com sucesso', { userId })
          importResults.imported.push('sessionData')
        } catch (error) {
          importResults.errors.push({ category: 'sessionData', error: error.message })
        }
      }

      if (options.gameProgress && backupData.data.gameProgress) {
        try {
          // Salvar progresso real dos jogos
          if (global.localStorage) {
            if (backupData.data.gameProgress.progressByGame) {
              // Salvar progresso individual por jogo
              Object.keys(backupData.data.gameProgress.progressByGame).forEach(gameId => {
                const key = `${gameId}_progress`
                global.localStorage.setItem(key, JSON.stringify(backupData.data.gameProgress.progressByGame[gameId]))
              })
            }
            
            if (backupData.data.gameProgress.userProgress) {
              global.localStorage.setItem('userProgress', JSON.stringify(backupData.data.gameProgress.userProgress))
            }
            
            // Salvar dados brutos de progresso
            Object.keys(backupData.data.gameProgress).forEach(key => {
              if (key.includes('_progress') || key.includes('_level') || key.includes('_achievements')) {
                global.localStorage.setItem(key, JSON.stringify(backupData.data.gameProgress[key]))
              }
            })
          }
          
          logger.info('Progresso dos jogos importado com sucesso', { userId })
          importResults.imported.push('gameProgress')
        } catch (error) {
          importResults.errors.push({ category: 'gameProgress', error: error.message })
        }
      }

      logger.info('Importação concluída', {
        userId,
        imported: importResults.imported.length,
        errors: importResults.errors.length
      })

      res.json({
        success: true,
        message: 'Importação concluída',
        results: importResults
      })

    } catch (error) {
      logger.error('Erro ao importar backup', {
        error: error.message,
        stack: error.stack,
        userId: req.body.userId
      })

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor ao importar backup',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  })

/**
 * GET /api/backup/status/:userId
 * Verifica status dos backups do usuário
 */
router.get('/status/:userId', async (req, res) => {
    try {
      const { userId } = req.params

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: 'userId é obrigatório'
        })
      }

      logger.info('Buscando status de backup', { userId })

      // Buscar status real de backup
      const backupStatus = {
        userId,
        lastBackup: null,
        totalBackups: 0,
        totalDataSize: 0,
        autoBackupEnabled: false,
        nextScheduledBackup: null,
        backupHistory: []
      }
      
      // Tentar recuperar histórico real de backups
      if (global.localStorage) {
        try {
          const backupHistory = JSON.parse(global.localStorage.getItem(`betina_backup_history_${userId}`) || '[]')
          const lastBackupData = global.localStorage.getItem(`betina_last_backup_${userId}`)
          
          if (backupHistory.length > 0) {
            backupStatus.totalBackups = backupHistory.length
            backupStatus.backupHistory = backupHistory.slice(-5) // Últimos 5 backups
            backupStatus.lastBackup = backupHistory[backupHistory.length - 1].date
            backupStatus.totalDataSize = backupHistory.reduce((acc, backup) => acc + (backup.size || 0), 0)
          }
          
          if (lastBackupData) {
            const lastBackupInfo = JSON.parse(lastBackupData)
            backupStatus.lastBackup = lastBackupInfo.timestamp
          }
          
          // Verificar configurações de auto-backup
          const autoBackupSettings = JSON.parse(global.localStorage.getItem(`betina_auto_backup_${userId}`) || '{}')
          backupStatus.autoBackupEnabled = autoBackupSettings.enabled || false
          backupStatus.nextScheduledBackup = autoBackupSettings.nextScheduled || null
          
        } catch (error) {
          logger.warn('Erro ao recuperar histórico de backup', { error: error.message })
        }
      }
      
      // Se não há dados reais, indicar usuário novo
      if (backupStatus.totalBackups === 0) {
        backupStatus.message = 'Nenhum backup encontrado - usuário novo ou dados não disponíveis'
        backupStatus.recommendation = 'Recomendamos fazer seu primeiro backup dos dados'
      }

      res.json({
        success: true,
        data: backupStatus
      })

    } catch (error) {
      logger.error('Erro ao buscar status de backup', {
        error: error.message,
        userId: req.params.userId
      })

      res.status(500).json({
        success: false,
        message: 'Erro ao buscar status de backup'
      })
    }
  })

/**
 * GET /api/backup/history/:userId
 * Busca histórico de backups do usuário
 */
router.get('/history/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const { limit = 10, offset = 0 } = req.query

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'userId é obrigatório'
      })
    }

    logger.info('Buscando histórico de backups', { userId, limit, offset })

    // Buscar histórico real de backups
    let realBackupHistory = []
    let totalBackups = 0
    
    if (global.localStorage) {
      try {
        const storedHistory = JSON.parse(global.localStorage.getItem(`betina_backup_history_${userId}`) || '[]')
        totalBackups = storedHistory.length
        
        // Aplicar paginação
        const startIndex = parseInt(offset)
        const endIndex = startIndex + parseInt(limit)
        realBackupHistory = storedHistory.slice(startIndex, endIndex)
        
      } catch (error) {
        logger.warn('Erro ao recuperar histórico', { error: error.message })
      }
    }
    
    // Se não há histórico real, retornar dados vazios
    if (realBackupHistory.length === 0) {
      realBackupHistory = []
      totalBackups = 0
    }

    res.json({
      success: true,
      data: {
        history: realBackupHistory,
        total: totalBackups,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < totalBackups
      }
    })

  } catch (error) {
    logger.error('Erro ao buscar histórico de backup', {
      error: error.message,
      userId: req.params.userId
    })

    res.status(500).json({
      success: false,
      message: 'Erro ao buscar histórico de backup'
    })
  }
})

/**
 * DELETE /api/backup/:userId/:backupId
 * Remove um backup específico
 */
router.delete('/:userId/:backupId', async (req, res) => {
  try {
    const { userId, backupId } = req.params

    if (!userId || !backupId) {
      return res.status(400).json({
        success: false,
        message: 'userId e backupId são obrigatórios'
      })
    }

    logger.info('Removendo backup', { userId, backupId })

    try {
      // Remover backup do histórico e armazenamento
      const backupHistory = getBackupHistory(userId);
      const backupToRemove = backupHistory.find(backup => backup.id === backupId);
      
      if (!backupToRemove) {
        return res.status(404).json({
          success: false,
          message: 'Backup não encontrado'
        });
      }
      
      const updatedHistory = backupHistory.filter(backup => backup.id !== backupId);
      
      // Salvar histórico atualizado
      saveBackupHistory(userId, updatedHistory);
      
      logger.info('Backup removido com sucesso', { userId, backupId });

      res.json({
        success: true,
        message: 'Backup removido com sucesso',
        data: { userId, backupId, removedAt: new Date().toISOString() }
      })
    } catch (error) {
      logger.error('Erro ao remover backup', { userId, backupId, error: error.message });
      
      res.status(500).json({
        success: false,
        message: 'Erro ao remover backup'
      })
      })
    }

  } catch (error) {
    logger.error('Erro ao remover backup', {
      error: error.message,
      userId: req.params.userId,
      backupId: req.params.backupId
    })

    res.status(500).json({
      success: false,
      message: 'Erro ao remover backup'
    })
  }
})

/**
 * POST /api/backup/validate
 * Valida um arquivo de backup
 */
router.post('/validate', async (req, res) => {
  try {
    const { backupData } = req.body

    if (!backupData) {
      return res.status(400).json({
        success: false,
        message: 'backupData é obrigatório'
      })
    }

    logger.info('Validando backup', {
      version: backupData.version,
      userId: backupData.userId
    })

    const validation = {
      valid: true,
      errors: [],
      warnings: [],
      info: {}
    }

    // Validar estrutura básica
    if (!backupData.version) {
      validation.errors.push('Versão do backup não encontrada')
      validation.valid = false
    } else if (backupData.version < '3.0.0') {
      validation.errors.push('Versão do backup não suportada. Versão mínima: 3.0.0')
      validation.valid = false
    }

    if (!backupData.exportDate) {
      validation.warnings.push('Data de exportação não encontrada')
    }

    if (!backupData.userId) {
      validation.errors.push('ID do usuário não encontrado')
      validation.valid = false
    }

    if (!backupData.data) {
      validation.errors.push('Dados do backup não encontrados')
      validation.valid = false
    } else {
      // Validar categorias de dados
      const categories = Object.keys(backupData.data)
      validation.info.categories = categories
      validation.info.totalCategories = categories.length
      validation.info.estimatedSize = JSON.stringify(backupData).length
    }

    // Validar metadados
    if (backupData.metadata) {
      validation.info.metadata = backupData.metadata
    } else {
      validation.warnings.push('Metadados não encontrados')
    }

    res.json({
      success: true,
      message: validation.valid ? 'Backup válido' : 'Backup inválido',
      validation: validation
    })

  } catch (error) {
    logger.error('Erro ao validar backup', {
      error: error.message,
      stack: error.stack
    })

    res.status(500).json({
      success: false,
      message: 'Erro ao validar backup',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
})

export default router
