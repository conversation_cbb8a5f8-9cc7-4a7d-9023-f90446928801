/**
 * Portal Betina V3 - API Server
 * Sistema terapêutico especializado para neurodivergência (autismo)
 * Utiliza algoritmos determinísticos para análise de métricas de jogos e sensores móveis
 */

// Importações estáticas para dependências críticas
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import { createLogger } from '../utils/logger.js';

// Configurar variáveis de ambiente
dotenv.config();

// Configurar logger profissional
const logger = createLogger('api-server');

// Importar sistema principal
import { initializePortalBetinaSystem } from './services/AppInitializer.js';


// Validar variáveis de ambiente críticas
function validateEnvironment() {
  const required = ['NODE_ENV', 'API_PORT'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Variáveis de ambiente obrigatórias não definidas: ${missing.join(', ')}`);
  }
}

// Importar middlewares de segurança
async function loadSecurityMiddleware() {
  try {
    const { dynamicCors } = await import('./middleware/security/cors.js');
    const { dynamicHelmet } = await import('./middleware/security/helmet.js');
    const { authenticate } = await import('./middleware/auth/jwt.js');
    
    return { cors: dynamicCors, helmet: dynamicHelmet, auth: authenticate };
  } catch (error) {
    logger.error('Erro ao carregar middlewares de segurança:', error);
    throw new Error('Falha ao carregar middlewares críticos de segurança');
  }
}

// Importar middlewares de monitoramento
async function loadMonitoringMiddleware() {
  try {
    const requestLogger = (await import('./middleware/logging/requestLogger.js')).default;
    const { errorHandler } = await import('./middleware/monitoring/errorHandler.js');
    
    return { requestLogger, errorHandler };
  } catch (error) {
    logger.error('Erro ao carregar middlewares de monitoramento:', error);
    throw new Error('Falha ao carregar middlewares de monitoramento');
  }
}

// Função para criar o app
async function createApp() {
  logger.info('🚀 Iniciando Portal Betina V3 API Server...');
  
  // Validar ambiente
  validateEnvironment();
  
  const app = express();
  const ENV = process.env.NODE_ENV || 'development';

  // Carregar middlewares
  const { cors: corsMiddleware, helmet: helmetMiddleware, auth } = await loadSecurityMiddleware();
  const { requestLogger, errorHandler } = await loadMonitoringMiddleware();

  // =====================================
  // CONFIGURAÇÕES DE SEGURANÇA
  // =====================================
  
  // Headers de segurança
  app.use(helmetMiddleware);
  
  // CORS configurado
  app.use(corsMiddleware);
  
  // Compressão
  app.use(compression());
  
  // Parsing com limites seguros
  app.use(express.json({ limit: '1mb' }));
  app.use(express.urlencoded({ extended: true, limit: '1mb' }));
  
  // Logging de requisições
  app.use(requestLogger);

  // =====================================
  // ROTAS PÚBLICAS
  // =====================================
  
  // Health check (sempre público)
  const healthRoutes = (await import('./routes/public/health.js')).default;
  app.use('/api/public/health', healthRoutes);
  
  // Jogos e atividades públicas
  const publicGamesRoutes = (await import('./routes/public/games.js')).default;
  const publicActivitiesRoutes = (await import('./routes/public/activities.js')).default;
  const publicMetricsRoutes = (await import('./routes/public/metrics.js')).default;
  app.use('/api/public/games', publicGamesRoutes);
  app.use('/api/public/activities', publicActivitiesRoutes);
  app.use('/api/public/metrics', publicMetricsRoutes);

  // =====================================
  // AUTENTICAÇÃO
  // =====================================
  
  const authRoutes = (await import('./routes/auth/simple-auth.js')).default;
  app.use('/api/auth', authRoutes);
  
  // Rotas específicas para dashboards
  const dashboardAuthRoutes = (await import('./routes/auth/dashboard-auth.js')).default;
  app.use('/api/auth/dashboard', dashboardAuthRoutes);

  // =====================================
  // ROTAS PROTEGIDAS - MÉTRICAS
  // =====================================
  
  // Métricas (requerem autenticação)
  app.use('/api/metrics', auth);
  const metricsSessionsRoutes = (await import('./routes/metrics/game-sessions.js')).default;
  const metricsInteractionsRoutes = (await import('./routes/metrics/interactions.js')).default;
  const metricsDashboardRoutes = (await import('./routes/metrics/dashboard.js')).default;
  
  // =====================================
  // ROTAS PREMIUM PROTEGIDAS - DASHBOARDS
  // =====================================
  
  const premiumDashboardRoutes = (await import('./routes/premium/dashboards.js')).default;
  app.use('/api/premium/dashboards', premiumDashboardRoutes);
  app.use('/api/metrics/sessions', metricsSessionsRoutes);
  app.use('/api/metrics/interactions', metricsInteractionsRoutes);
  app.use('/api/metrics/dashboard', metricsDashboardRoutes);

  // =====================================
  // ROTAS PREMIUM (PROTEGIDAS)
  // =====================================
  
  // Dashboards (requerem autenticação)
  app.use('/api/dashboard', auth);
  const dashboardTherapeuticRoutes = (await import('./routes/dashboard/therapeutic.js')).default;
  app.use('/api/dashboard/therapeutic', dashboardTherapeuticRoutes);
  
  // 🔗 NOVO: Vinculação de perfis ao dashboard
  const profileLinkingRoutes = (await import('./routes/dashboard/profile-linking.js')).default;
  app.use('/api/dashboard', profileLinkingRoutes);
  
  // Relatórios (requerem autenticação)
  app.use('/api/reports', auth);
  const reportsTherapeuticRoutes = (await import('./routes/reports/therapeutic.js')).default;
  app.use('/api/reports/therapeutic', reportsTherapeuticRoutes);

  // Backup e Exportação (temporariamente sem autenticação para teste)
  const backupUserRoutes = (await import('./routes/backup/user-backup.js')).default;
  app.use('/api/backup', backupUserRoutes);

  // =====================================
  // INFORMAÇÕES DO SISTEMA (PROTEGIDO)
  // =====================================
  
  app.get('/api/info', auth, (req, res) => {
    res.json({
      success: true,
      data: {
        name: 'Portal Betina V3 API',
        version: '3.0.0',
        environment: ENV,
        timestamp: new Date().toISOString()
      }
    });
  });

  // =====================================
  // TRATAMENTO DE ERROS
  // =====================================
  
  // Rota não encontrada
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      message: 'Endpoint não encontrado',
      error_code: 'ENDPOINT_NOT_FOUND'
    });
  });
  
  // Middleware de tratamento de erros
  app.use(errorHandler);

  return app;
}

// Função para iniciar o servidor
async function startServer() {
  try {
    logger.info('🔄 Inicializando sistema integrado...');
    
    // Inicializar SystemOrchestrator (obrigatório)
    const systemInstance = await initializePortalBetinaSystem();
    if (!systemInstance || !systemInstance.systemOrchestrator) {
      throw new Error('Falha crítica: SystemOrchestrator não foi inicializado');
    }
    
    // Health check do SystemOrchestrator
    try {
      // Verificar se o orquestrador está funcional
      logger.info('✅ SystemOrchestrator inicializado e funcional');
    } catch (error) {
      throw new Error(`SystemOrchestrator falhou no health check: ${error.message}`);
    }

    // Criar aplicação
    const app = await createApp();
    const PORT = parseInt(process.env.API_PORT) || 3000;
    const ENV = process.env.NODE_ENV || 'development';

    // Disponibilizar SystemOrchestrator para as rotas
    app.use((req, res, next) => {
      req.systemInstance = systemInstance;
      req.systemOrchestrator = systemInstance.systemOrchestrator;
      next();
    });

    // Iniciar servidor
    const server = app.listen(PORT, () => {
      logger.info(`🚀 Portal Betina V3 API Server iniciado!`);
      logger.info(`📍 Ambiente: ${ENV}`);
      logger.info(`🌐 Porta: ${PORT}`);
      logger.info(`💻 URL: http://localhost:${PORT}`);
      logger.info(`🔒 Health: http://localhost:${PORT}/api/public/health`);
      logger.info(`✅ SystemOrchestrator: Operacional`);
    });

    // Graceful shutdown
    const gracefulShutdown = (signal) => {
      logger.info(`🛑 Recebido ${signal}. Encerrando servidor graciosamente...`);
      server.close(async () => {
        try {
          // Cleanup do SystemOrchestrator se necessário
          if (systemInstance && systemInstance.cleanup) {
            await systemInstance.cleanup();
          }
          logger.info('✅ Servidor encerrado com sucesso.');
          process.exit(0);
        } catch (error) {
          logger.error('Erro durante o shutdown:', error);
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    return server;
  } catch (error) {
    logger.error('❌ Erro fatal ao iniciar o servidor:', error);
    throw error;
  }
}

// Iniciar servidor apenas se não estivermos em ambiente de teste
if (process.env.NODE_ENV !== 'test') {
  startServer().catch(error => {
    logger.error('❌ Falha na inicialização:', error);
    process.exit(1);
  });
}

// Exportar para testes
export { createApp, startServer };
