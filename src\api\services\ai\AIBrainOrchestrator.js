/**
 * @file AIBrainOrchestrator.js
 * @description Orquestrador supremo com IA para análise avançada, relatórios personalizados e predições
 * @version 3.2.4 - Integração com DeepSeek Chat para análise inteligente
 * <AUTHOR> Betina V3
  @signature DeepSeek Chat, o Farol da Mente Infantil
 */

import { SUPPORTED_GAMES, COGNITIVE_DOMAINS } from '../../config/supportedGames.js';

// 📝 Sistema de Logs Estruturados
import { StructuredLogger } from '../core/logging/StructuredLogger.js';

// 💾 Cache Inteligente
import { IntelligentCache } from '../core/cache/IntelligentCache.js';

// Constantes para análise multissensorial
const MULTISENSORY_MODALITIES = {
  VISUAL: 'visual',
  AUDITORY: 'auditory',
  TACTILE: 'tactile',
  MOVEMENT: 'movement'
};

const SENSOR_THRESHOLDS = {
  ATTENTION_SPAN: {
    LONG: 3000,
    MEDIUM: 1500
  },
  PROCESSING_SPEED: {
    FAST: 500,
    MEDIUM: 1000
  },
  COGNITIVE_LOAD: {
    HIGH: 0.8,
    MEDIUM: 0.5
  },
  ACCURACY: {
    HIGH: 0.8,
    MEDIUM: 0.6
  }
};

// Constantes adicionais para análise multissensorial
const SENSORY_MODALITIES = [
  'visual',
  'auditory',
  'tactile',
  'movement'
];

const MULTISENSORY_THRESHOLDS = {
  high_performance: 0.8,
  medium_performance: 0.6,
  low_performance: 0.4
};

// Detectar ambiente
const isNode = typeof process !== 'undefined' && process.versions && process.versions.node;
const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';

// Função para obter variáveis de ambiente de forma segura
function getEnvVar(name, defaultValue = '') {
  if (isNode && typeof process !== 'undefined') {
    return process.env[name] || defaultValue;
  } else if (isBrowser) {
    // No browser, usar import.meta.env ou window.env
    return import.meta.env?.[name] || window.env?.[name] || defaultValue;
  }
  return defaultValue;
}

/**
 * @class AIBrainOrchestrator
 * @description Cérebro supremo que usa IA para análises profundas e relatórios cativantes para pais
 */
class AIBrainOrchestrator {
  constructor(config = {}) {
    this.apiConfigs = [
      {
        key: getEnvVar('AI_API_KEY') || config.apiKey || '',
        endpoint: getEnvVar('AI_API_ENDPOINT') || config.apiEndpoint || 'https://api.deepseek.com/v1',
        model: getEnvVar('AI_API_MODEL') || config.apiModel || 'deepseek-chat',
        priority: 1
      },
      // Adicione outras APIs (e.g., OpenAI, Hugging Face) no .env ou config
      ...(config.additionalAPIs || [])
    ];
    this.retryAttempts = Number(getEnvVar('AI_RETRY_ATTEMPTS')) || config.retryAttempts || 3;
    this.retryDelay = Number(getEnvVar('AI_RETRY_DELAY')) || config.retryDelay || 1000;
    this.cacheMaxSize = Number(getEnvVar('AI_CACHE_MAX_SIZE')) || config.cacheMaxSize || 1000;
    this.cacheTTL = Number(getEnvVar('AI_CACHE_TTL')) || config.cacheTTL || 3600000; // 1 hora
    this.databaseService = config.databaseService || null; // Carregado dinamicamente se necessário
    this.systemOrchestrator = config.systemOrchestrator || null; // Referência ao SystemOrchestrator

    // 📝 Integrar Sistema de Logs Estruturados
    this.logger = StructuredLogger.getInstance({
      serviceName: 'AIBrainOrchestrator',
      logLevel: config.logLevel || 'info'
    });

    // 💾 Integrar Cache Inteligente para análises de IA
    this.cache = new IntelligentCache({
      maxSize: this.cacheMaxSize,
      defaultTTL: this.cacheTTL,
      strategy: 'LRU',
      compressionThreshold: 5120, // 5KB para dados de IA
      compress: true // IA gera dados grandes
    });

    this.supportedGames = SUPPORTED_GAMES;
    this.cognitiveDomains = COGNITIVE_DOMAINS;
    this.feedbackHistory = new Map(); // Armazena feedback de atividades

    this.logger.info('🧠 AIBrainOrchestrator inicializado - Modo Supremo', {
      type: 'ai_brain_init',
      version: '3.2.3',
      mode: 'supremo',
      apiConfigs: this.apiConfigs.map(c => ({ endpoint: c.endpoint, model: c.model })),
      supportedGames: this.supportedGames,
      cacheEnabled: true,
      cacheMaxSize: this.cacheMaxSize,
      cacheTTL: this.cacheTTL,
      timestamp: new Date().toISOString()
    });
  }



  /**
   * Valida métricas de entrada para um jogo
   * @param {string} gameName - Nome do jogo
   * @param {Object} metrics - Métricas do jogo
   * @returns {Object} Métricas validadas
   * @throws {Error} Se a validação falhar
   * @private
   */
  validateMetrics(gameName, metrics) {
    try {
      if (!this.supportedGames.includes(gameName)) {
        throw new Error(`Jogo não suportado: ${gameName}. Jogos suportados: ${this.supportedGames.join(', ')}`);
      }
      if (!metrics?.childId || !metrics?.sessionId) {
        throw new Error('childId e sessionId são obrigatórios');
      }

      const validated = {
        ...metrics,
        timestamp: metrics.timestamp || new Date().toISOString(),
        gameName,
        accuracy: Number(metrics.accuracy) || 0.5,
        responseTime: Number(metrics.responseTime) || 5000,
        engagement: Number(metrics.engagement) || 0.5
      };

      switch (gameName) {
        case 'ImageAssociation':
          validated.category = metrics.category || null;
          validated.correct = metrics.correct ?? false;
          break;
        case 'MemoryGame':
          validated.pairFound = metrics.pairFound ?? false;
          validated.cardPosition = metrics.cardPosition || [];
          break;
        case 'MusicalSequence':
          validated.sequenceLength = Number(metrics.sequenceLength) || 0;
          validated.expectedResponseTime = Number(metrics.expectedResponseTime) || 0;
          break;
        case 'PadroesVisuais':
          validated.patternComplexity = Number(metrics.patternComplexity) || 1;
          validated.correct = metrics.correct ?? false;
          break;
        case 'ContagemNumeros':
          validated.score = Number(metrics.score) || 0;
          validated.correct = metrics.correct ?? false;
          break;
        case 'PatternMatching':
          validated.correctMatch = metrics.correctMatch ?? false;
          validated.patternComplexity = Number(metrics.patternComplexity) || 1;
          break;
        case 'SequenceLearning':
          validated.isSequence = metrics.isSequence ?? false;
          validated.sequenceLength = Number(metrics.sequenceLength) || 0;
          break;
        case 'CreativePainting':
          validated.strokes = Number(metrics.strokes) || 0;
          validated.creationTime = Number(metrics.creationTime) || 0;
          validated.colorDiversity = Number(metrics.colorDiversity) || 0;
          break;
      }

      this.logger.info('✅ Métricas validadas', { gameName, sessionId: validated.sessionId });
      return validated;
    } catch (error) {
      this.logger.error('❌ Erro ao validar métricas', { gameName, error: error.message });
      throw error;
    }
  }

  /**
   * 🔬 Registrar analisadores especializados
   * @param {Object} analyzers - Objeto com analisadores especializados
   */
  registerAnalyzers(analyzers) {
    try {
      this.specializedAnalyzers = {
        behavioral: analyzers.behavioral || null,
        cognitive: analyzers.cognitive || null,
        progress: analyzers.progress || null,
        session: analyzers.session || null,
        therapeutic: analyzers.therapeutic || null
      };

      // Registrar analisadores nos processadores específicos
      Object.entries(this.processors).forEach(([gameType, processor]) => {
        if (processor && typeof processor.registerAnalyzers === 'function') {
          processor.registerAnalyzers(this.specializedAnalyzers);
        }
      });

      this.logger.info('🔬 Analisadores especializados registrados no AI Brain', {
        type: 'analyzers_registered',
        analyzers: Object.keys(analyzers),
        processorsUpdated: Object.keys(this.processors).length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('❌ Erro ao registrar analisadores', {
        type: 'analyzers_registration_error',
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * 🔬 Obter analisadores registrados
   * @returns {Object} - Analisadores especializados
   */
  getRegisteredAnalyzers() {
    return this.specializedAnalyzers || {};
  }

  /**
   * 🔬 Executar análise especializada
   * @param {string} analyzerType - Tipo de analisador
   * @param {Object} data - Dados para análise
   * @returns {Promise<Object>} - Resultado da análise
   */
  async runSpecializedAnalysis(analyzerType, data) {
    try {
      const analyzer = this.specializedAnalyzers?.[analyzerType];

      if (!analyzer) {
        this.logger.warn(`⚠️ Analisador ${analyzerType} não encontrado`, {
          type: 'analyzer_not_found',
          analyzerType,
          availableAnalyzers: Object.keys(this.specializedAnalyzers || {})
        });
        return { success: false, error: `Analyzer ${analyzerType} not found` };
      }

      // Executar análise baseada no tipo
      let result;
      switch (analyzerType) {
        case 'behavioral':
          result = await analyzer.analyzeBehavioralPatterns(data);
          break;
        case 'cognitive':
          result = await analyzer.analyzeCognitiveFunctions(data);
          break;
        case 'progress':
          result = await analyzer.analyzeProgress(data);
          break;
        case 'session':
          result = await analyzer.analyzeSession(data);
          break;
        case 'therapeutic':
          result = await analyzer.analyzeTherapeuticOutcomes(data);
          break;
        default:
          throw new Error(`Unknown analyzer type: ${analyzerType}`);
      }

      this.logger.info(`🔬 Análise ${analyzerType} concluída`, {
        type: 'specialized_analysis_complete',
        analyzerType,
        success: result?.success !== false,
        timestamp: new Date().toISOString()
      });

      return result;

    } catch (error) {
      this.logger.error(`❌ Erro na análise ${analyzerType}`, {
        type: 'specialized_analysis_error',
        analyzerType,
        error: error.message,
        stack: error.stack
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * Chama a API de IA com retry e suporte multi-modelo
   * @param {Object} metrics - Métricas brutas
   * @param {Object} analysis - Análise do SystemOrchestrator
   * @param {Object} multisensoryData - Dados multissensoriais (opcional)
   * @returns {Promise<Object>} Resultado da IA
   * @private
   */
  async callAIAPI(metrics, analysis, multisensoryData = null) {
    const cacheKey = `ai_analysis_${JSON.stringify({ 
      childId: metrics.childId, 
      gameName: metrics.gameName, 
      hash: this.generateHash(metrics, analysis) 
    })}`;
    
    // Verificar cache com tags inteligentes
    const cached = this.cache.get(cacheKey);
    if (cached) {
      this.logger.info('📥 Resposta obtida do cache', { cacheKey });
      return cached;
    }

    // Tags para invalidação inteligente
    const tags = [
      `child_${metrics.childId}`,
      `game_${metrics.gameName}`,
      'ai_analysis',
      `date_${new Date().toISOString().split('T')[0]}`
    ];

    // Gerenciar tamanho do cache
    if (this.cache.size >= this.cacheMaxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
      this.logger.warn('🗑️ Cache cheio, removendo entrada mais antiga', { oldestKey });
    }

    for (const apiConfig of this.apiConfigs.sort((a, b) => a.priority - b.priority)) {
      for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
        try {
          if (!apiConfig.key || !apiConfig.endpoint) {
            this.logger.warn('⚠️ API de IA não configurada, tentando próxima', { endpoint: apiConfig.endpoint });
            continue;
          }

          const response = await fetch(`${apiConfig.endpoint}/completions`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiConfig.key}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              model: apiConfig.model,
              prompt: this.buildAIPrompt(metrics, analysis, apiConfig.model, multisensoryData),
              max_tokens: 1500,
              temperature: 0.7,
              top_p: 0.9
            })
          });

          if (!response.ok) {
            throw new Error(`Erro na API: ${response.status} - ${response.statusText}`);
          }

          const result = await response.json();
          const aiResponse = {
            text: result.choices[0]?.text || '',
            confidence: result.choices[0]?.confidence || 0.9,
            metadata: { source: 'api', model: apiConfig.model, ...result.metadata }
          };
          
          // Armazenar no cache com tags para invalidação inteligente
          this.cache.set(cacheKey, { ...aiResponse, timestamp: Date.now() }, {
            tags,
            ttl: this.cacheTTL
          });
          
          this.logger.success('🌟 Resposta da API de IA recebida', { model: apiConfig.model, attempt });
          return aiResponse;
        } catch (error) {
          this.logger.error('❌ Erro ao chamar API de IA', { attempt, endpoint: apiConfig.endpoint, error: error.message });
          if (attempt === this.retryAttempts) {
            this.logger.warn('⚠️ Tentativas esgotadas para API', { endpoint: apiConfig.endpoint });
          }
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        }
      }
    }

    const fallback = await this.localAIAnalysis(metrics, analysis);
    this.cache.set(cacheKey, { ...fallback, timestamp: Date.now() }, {
      tags,
      ttl: this.cacheTTL
    });
    return fallback;
  }

  /**
   * Constrói prompt para a IA com personalização por modelo
   * @param {Object} metrics - Métricas brutas
   * @param {Object} analysis - Análise do SystemOrchestrator
   * @param {string} model - Modelo de IA
   * @param {Object} multisensoryData - Dados multissensoriais (opcional)
   * @returns {string} Prompt formatado
   * @private
   */
  buildAIPrompt(metrics, analysis, model, multisensoryData = null) {
    const gameName = metrics.gameName || 'análise cruzada';
    const strengths = analysis.specificAnalysis?.strengths || analysis.analyses?.strengths || [];
    const weaknesses = analysis.specificAnalysis?.weaknesses || analysis.analyses?.weaknesses || [];
    const progress = analysis.therapeuticAnalysis?.progressIndicators || analysis.progress || {};
    const cognitiveDomains = this.cognitiveDomains[gameName] || Object.keys(analysis.analises?.cognitiveDomains || {});

    // Incluir informações multissensoriais se disponíveis
    let multisensoryInfo = '';
    if (multisensoryData && multisensoryData.success) {
      const insights = multisensoryData.insights || {};
      const modality = multisensoryData.modalityPreferences?.primary || 'visual';
      const adaptations = multisensoryData.insights?.adaptations || [];
      
      multisensoryInfo = `
**Análise Multissensorial:**
- Modalidade preferencial: ${modality}
- Forças sensoriais: ${insights.strengths?.join(', ') || 'Não identificadas'}
- Desafios sensoriais: ${insights.challenges?.join(', ') || 'Nenhum crítico'}
- Adaptações recomendadas: ${adaptations.map(a => a.description).join(', ') || 'Nenhuma específica'}
- Padrões identificados: ${JSON.stringify(multisensoryData.sensorPatterns || {}, null, 2)}
`;
    }

    const modelSpecific = model.includes('deepseek') ? 'Use um tom inspirador e metáforas criativas.' : 'Priorize clareza e concisão.';
    return `
Você é um assistente terapêutico especializado em interpretar métricas de jogos educativos para pais, em linguagem simples, amigável e envolvente. Sua tarefa é analisar o desempenho de uma criança no jogo "${gameName}" e gerar um relatório que:
1. Destaque o que a criança faz bem, com metáforas cativantes (e.g., "como um explorador encontrando tesouros").
2. Indique áreas para melhorar, com tom positivo.
3. Sugira 3-5 atividades práticas para casa, conectadas às áreas cognitivas.
4. Use o nome da criança, se disponível (ex.: "${metrics.childName || 'sua criança'}").
5. Crie uma narrativa que envolva emocionalmente os pais.
6. Considere as preferências sensoriais da criança para sugestões mais personalizadas.

**Dados:**
- Jogo: ${gameName}
- Áreas cognitivas: ${cognitiveDomains.join(', ')}
- Forças: ${strengths.join(', ') || 'Nenhuma identificada'}
- Áreas de desenvolvimento: ${weaknesses.join(', ') || 'Nenhuma crítica'}
- Progresso: ${JSON.stringify(progress, null, 2)}
- Métricas brutas: ${JSON.stringify(metrics, null, 2)}
${multisensoryInfo}

**Instruções:**
- Escreva em português, sem jargões técnicos (e.g., use "lembrar o que vê" em vez de "memória visual").
- ${modelSpecific}
- Máximo de 600 palavras.
- Inclua uma história curta (2-3 frases) sobre o progresso da criança.
- Sugira atividades práticas e fáceis de implementar.
- Se houver dados multissensoriais, adapte as sugestões à modalidade preferencial da criança.

Exemplo:
"${metrics.childName || 'Sua criança'} está brilhando no jogo de associação de imagens! Como um detetive juntando pistas, ela conecta ideias rapidamente. Ela explorou a floresta das imagens e encontrou muitos tesouros! Às vezes, demora um pouco para responder, mas isso é normal. Para ajudar, brinque de 'qual objeto combina com este?', como associar uma maçã com a cor vermelha. Outra ideia é criar histórias com brinquedos. Continue incentivando, ela está no caminho certo!"
`;
  }

  /**
   * Análise local avançada como fallback
   * @param {Object} metrics - Métricas brutas
   * @param {Object} analysis - Análise do SystemOrchestrator
   * @returns {Object} Resultado da análise local
   * @private
   */
  async localAIAnalysis(metrics, analysis) {
    try {
      const gameName = metrics.gameName || 'análise cruzada';
      const childName = metrics.childName || 'sua criança';
      const strengths = analysis.specificAnalysis?.strengths || analysis.analyses?.strengths || [];
      const weaknesses = analysis.specificAnalysis?.weaknesses || analysis.analises?.weaknesses || [];
      const cognitiveDomains = this.cognitiveDomains[gameName] || Object.keys(analysis.analises?.cognitiveDomains || []);

      let story = `${childName} embarcou numa aventura incrível no ${gameName}! `;
      if (strengths.length > 0) {
        story += `Como um super-herói, ${childName} brilhou em ${this.translateDomains(strengths)}. `;
      }
      if (weaknesses.length > 0) {
        story += `Às vezes, ${childName} precisa de um pouco mais de prática em ${this.translateDomains(weaknesses)}, mas está no caminho certo! `;
      }

      const activities = this.generateActivitySuggestions(gameName, weaknesses, metrics.childName);
      const report = `${story} Para ajudar ${childName} a crescer ainda mais, experimente estas brincadeiras em casa: ${activities.join(' ')} Parabéns por apoiar ${childName} nessa jornada!`;

      this.logger.warn('⚠️ Usando análise local avançada', { gameName });
      return {
        text: report,
        confidence: 0.75,
        metadata: { source: 'local', story: true }
      };
    } catch (error) {
      this.logger.error('❌ Erro na análise local', { error: error.message });
      return {
        text: 'Não foi possível gerar o relatório. Tente novamente mais tarde.',
        confidence: 0.5,
        metadata: { source: 'local', error: error.message }
      };
    }
  }

  /**
   * Traduz domínios cognitivos para linguagem simples
   * @param {Array} domains - Domínios cognitivos
   * @returns {string} Tradução
   * @private
   */
  translateDomains(domains) {
    const translations = {
      'associação conceitual': 'conectar ideias como um detetive',
      'pensamento categórico': 'organizar coisas por grupos',
      'memória semântica': 'lembrar significados',
      'memória de trabalho': 'manter informações na cabeça',
      'memória visual': 'lembrar o que vê',
      'atenção': 'focar nas tarefas',
      'memória auditiva': 'lembrar sons e músicas',
      'processamento sequencial': 'seguir passos em ordem',
      'percepção rítmica': 'sentir o ritmo',
      'reconhecimento de padrões': 'encontrar formas repetidas',
      'processamento espacial': 'entender posições e espaços',
      'raciocínio lógico': 'resolver problemas como um quebra-cabeça',
      'cognição numérica': 'trabalhar com números',
      'raciocínio matemático': 'pensar com números',
      'aprendizado sequencial': 'aprender sequências',
      'expressão criativa': 'criar e imaginar',
      'coordenação motora': 'usar as mãos com precisão',
      'cobertura espacial': 'usar todo o espaço ao desenhar'
    };
    return domains.map(d => translations[d] || d).join(', ');
  }

  /**
   * Gera sugestões de atividades práticas adaptativas
   * @param {string} gameName - Nome do jogo ou 'cross'
   * @param {Array} weaknesses - Áreas de desenvolvimento
   * @param {string} [childName] - Nome da criança
   * @returns {Array} Sugestões de atividades
   * @private
   */
  generateActivitySuggestions(gameName, weaknesses, childName = 'sua criança') {
    const baseSuggestions = {
      ImageAssociation: [
        `Brinque com ${childName} de "qual objeto combina com este?" usando brinquedos ou imagens.`,
        `Crie histórias com ${childName} usando objetos do dia a dia para estimular a imaginação.`,
        `Mostre imagens e peça para ${childName} agrupá-las por temas, como animais ou cores.`
      ],
      MemoryGame: [
        `Jogue um jogo de memória com ${childName} usando cartas ou objetos em casa.`,
        `Peça para ${childName} lembrar a ordem de brinquedos em uma fileira.`,
        `Esconda objetos e peça para ${childName} lembrar onde estão.`
      ],
      MusicalSequence: [
        `Cante músicas com ritmos simples e peça para ${childName} repetir.`,
        `Use palmas com ${childName} para criar sequências rítmicas juntos.`,
        `Toque sons com objetos (como colheres) e peça para ${childName} imitar.`
      ],
      PadroesVisuais: [
        `Monte quebra-cabeças ou desenhe padrões com ${childName} usando blocos coloridos.`,
        `Peça para ${childName} encontrar formas repetidas em revistas.`,
        `Crie sequências de cores com lápis e peça para ${childName} continuar o padrão.`
      ],
      ContagemNumeros: [
        `Conte objetos do cotidiano com ${childName}, como frutas na cozinha.`,
        `Brinque de contar passos com ${childName} durante uma caminhada.`,
        `Use brinquedos para praticar somas simples com ${childName}, como "2 carrinhos + 3 carrinhos".`
      ],
      PatternMatching: [
        `Crie jogos de combinar formas ou cores com ${childName} usando cartões.`,
        `Desenhe padrões simples e peça para ${childName} completá-los.`,
        `Use objetos para criar pares e peça para ${childName} encontrar os iguais.`
      ],
      SequenceLearning: [
        `Crie sequências de ações com ${childName}, como "pular, girar, bater palmas".`,
        `Use brinquedos com ${childName} para montar uma ordem específica.`,
        `Peça para ${childName} repetir uma sequência de movimentos que você fizer.`
      ],
      CreativePainting: [
        `Dê papel e tintas para ${childName} desenhar livremente.`,
        `Peça para ${childName} cobrir todo o papel com cores diferentes.`,
        `Crie desenhos com ${childName}, incentivando o uso de muitas formas e cores.`
      ],
      cross: weaknesses.length > 0 ? [
        `Combine atividades para ${childName}, como contar objetos enquanto desenha formas.`,
        `Crie jogos que misturem ${weaknesses.join(' e ')} com ${childName}, como sequências com números e formas.`,
        `Use histórias com ${childName} para praticar várias habilidades ao mesmo tempo.`
      ] : [`Tente brincadeiras criativas com ${childName} que misturem várias habilidades.`]
    };

    // Ajustar sugestões com base no feedback
    const feedback = this.feedbackHistory.get(`${childName}-${gameName}`) || {};
    const suggestions = baseSuggestions[gameName].filter(s => !feedback[s]?.tested || feedback[s]?.success);
    return suggestions.length > 0 ? suggestions : baseSuggestions[gameName];
  }

  /**
   * Processa métricas de um jogo e gera relatório com IA
   * @param {string} gameName - Nome do jogo
   * @param {Object} metrics - Métricas brutas
   * @param {Object} multisensoryData - Dados multissensoriais opcionais
   * @returns {Promise<Object>} Relatório e análise
   */
  async processGameMetrics(gameName, metrics, multisensoryData = null) {
    try {
      // 💾 Verificar cache primeiro
      const cacheKey = `ai_analysis_${gameName}_${JSON.stringify(metrics).slice(0, 50)}_${multisensoryData ? 'multi' : 'basic'}`;
      let cachedResult = this.cache.get(cacheKey);

      if (cachedResult) {
        this.logger.debug('💾 Cache hit para análise de IA', {
          gameName,
          cacheKey: cacheKey.slice(0, 40) + '...',
          type: 'ai_cache_hit'
        });
        return cachedResult;
      }

      // Validar se o jogo é suportado
      if (!this.isGameSupported(gameName)) {
        this.logger.error(`❌ Jogo não suportado: ${gameName}`, {
          type: 'game_not_supported',
          gameName,
          supportedGames: this.supportedGames
        });
        return {
          success: false,
          error: `Jogo '${gameName}' não é suportado pelo AIBrainOrchestrator`,
          supportedGames: this.supportedGames
        };
      }

      const validatedMetrics = this.validateMetrics(gameName, metrics);

      // Processar dados multissensoriais se disponíveis
      let multisensoryAnalysis = null;
      if (multisensoryData) {
        this.logger.info('🧠 Processando dados multissensoriais', {
          sensorTypes: Object.keys(multisensoryData),
          childId: validatedMetrics.childId
        });
        multisensoryAnalysis = await this.processMultisensoryData(multisensoryData, validatedMetrics);
      }

      // Verificar se SystemOrchestrator está disponível
      let systemAnalysis = null;
      if (this.systemOrchestrator && typeof this.systemOrchestrator.processGameMetrics === 'function') {
        try {
          systemAnalysis = await this.systemOrchestrator.processGameMetrics(
            validatedMetrics.childId,
            gameName,
            validatedMetrics
          );
        } catch (error) {
          this.logger.warn('⚠️ SystemOrchestrator não disponível, usando análise básica', { error: error.message });
          systemAnalysis = this.generateFallbackAnalysis(validatedMetrics, gameName);
        }
      } else {
        this.logger.warn('⚠️ SystemOrchestrator não configurado, usando análise básica');
        systemAnalysis = this.generateFallbackAnalysis(validatedMetrics, gameName);
      }

      if (!systemAnalysis.success) {
        throw new Error(systemAnalysis.error || 'Falha no processamento pelo SystemOrchestrator');
      }

      const aiResult = await this.callAIAPI(validatedMetrics, systemAnalysis, multisensoryAnalysis);
      const report = await this.formatParentReport(aiResult.text, systemAnalysis, validatedMetrics.childName);
      
      // Incluir análise multissensoriais no relatório se disponível
      if (multisensoryAnalysis && multisensoryAnalysis.success) {
        report.multisensoryInsights = multisensoryAnalysis.insights;
        report.modalityPreferences = multisensoryAnalysis.modalityPreferences;
        report.sensorPatterns = multisensoryAnalysis.sensorPatterns;
        report.adaptations = multisensoryAnalysis.insights.adaptations;
      }

      // Salvar no banco se o databaseService estiver disponível
      if (this.databaseService && typeof this.databaseService.store === 'function') {
        try {
          await this.databaseService.store('ai_analysis', {
            gameName,
            childId: validatedMetrics.childId,
            sessionId: validatedMetrics.sessionId,
            timestamp: validatedMetrics.timestamp,
            aiReport: report,
            rawMetrics: validatedMetrics,
            systemAnalysis,
            aiConfidence: aiResult.confidence
          });
        } catch (error) {
          this.logger.warn('⚠️ Erro ao salvar no banco, continuando sem persistência', { error: error.message });
        }
      }

      const result = {
        success: true,
        report,
        systemAnalysis,
        multisensoryAnalysis,
        aiConfidence: aiResult.confidence,
        metadata: aiResult.metadata
      };

      // 💾 Cachear resultado da análise de IA
      this.cache.set(cacheKey, result, this.cacheTTL);

      this.logger.info('🌟 Relatório gerado e cacheado com sucesso', {
        type: 'ai_analysis_complete',
        gameName,
        childId: validatedMetrics.childId,
        cacheKey: cacheKey.slice(0, 40) + '...',
        aiConfidence: aiResult.confidence,
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      this.logger.error('❌ Erro ao processar métricas com IA', { gameName, error: error.message });
      return {
        success: false,
        error: error.message,
        report: 'Não foi possível gerar o relatório. Tente novamente.',
        systemAnalysis: {},
        aiConfidence: 0,
        metadata: {}
      };
    }
  }

  /**
   * Formata relatório para pais com storytelling
   * @param {string} aiText - Texto gerado pela IA
   * @param {Object} systemAnalysis - Análise do SystemOrchestrator
   * @param {string} [childName] - Nome da criança
   * @returns {Object} Relatório formatado
   * @private
   */
  async formatParentReport(aiText, systemAnalysis, childName = 'sua criança') {
    try {
      const gameName = systemAnalysis.gameName;
      const strengths = systemAnalysis.specificAnalysis?.strengths || [];
      const weaknesses = systemAnalysis.specificAnalysis?.weaknesses || [];
      const activities = this.generateActivitySuggestions(gameName, weaknesses, childName);
      const visualData = this.generateVisualData(systemAnalysis);

      return {
        summary: aiText || `${childName} está progredindo no ${gameName} como um verdadeiro explorador!`,
        strengths: strengths.length > 0 ? strengths.map(s => this.translateDomains([s])[0]) : ['Engajamento com a atividade'],
        weaknesses: weaknesses.length > 0 ? weaknesses.map(w => this.translateDomains([w])[0]) : ['Nenhuma área crítica'],
        activities,
        visualData,
        timestamp: new Date().toISOString(),
        confidence: systemAnalysis.metadata?.dataQuality || 0.5,
        childName
      };
    } catch (error) {
      this.logger.error('❌ Erro ao formatar relatório', { error: error.message });
      return {
        summary: 'Não foi possível gerar o relatório completo.',
        strengths: [],
        weaknesses: [],
        activities: [],
        visualData: {},
        timestamp: new Date().toISOString(),
        confidence: 0.5,
        childName
      };
    }
  }

  /**
   * Gera dados visuais para gráficos
   * @param {Object} systemAnalysis - Análise do SystemOrchestrator
   * @returns {Object} Dados para visualização
   * @private
   */
  generateVisualData(systemAnalysis) {
    try {
      const metrics = systemAnalysis.specificAnalysis?.baseMetrics || {};
      return {
        accuracy: {
          value: metrics.accuracy || 0.5,
          label: 'Precisão',
          color: '#4CAF50'
        },
        responseTime: {
          value: metrics.responseTime ? 1000 / metrics.responseTime : 0.5,
          label: 'Velocidade',
          color: '#2196F3'
        },
        engagement: {
          value: metrics.engagement || 0.5,
          label: 'Engajamento',
          color: '#FF9800'
        }
      };
    } catch (error) {
      this.logger.error('❌ Erro ao gerar dados visuais', { error: error.message });
      return {};
    }
  }

  /**
   * Gera análise cruzada de múltiplos jogos
   * @param {string} childId - ID da criança
   * @param {Array} gameData - Lista de dados de jogos
   * @returns {Promise<Object>} Análise cruzada
   */
  async crossGameAnalysis(childId, gameData) {
    try {
      if (!childId) {
        throw new Error('childId é obrigatório');
      }
      if (!Array.isArray(gameData) || gameData.length === 0) {
        throw new Error('gameData deve ser uma lista não vazia');
      }

      const analyses = [];
      for (const data of gameData) {
        const validated = this.validateMetrics(data.gameName, data.metrics);
        
        // Verificar se SystemOrchestrator está disponível
        let analysis = null;
        if (this.systemOrchestrator && typeof this.systemOrchestrator.processGameMetrics === 'function') {
          try {
            analysis = await this.systemOrchestrator.processGameMetrics(childId, data.gameName, validated);
          } catch (error) {
            this.logger.warn('⚠️ SystemOrchestrator não disponível, usando análise básica', { error: error.message });
            analysis = this.generateFallbackAnalysis(validated, data.gameName);
          }
        } else {
          this.logger.warn('⚠️ SystemOrchestrator não configurado, usando análise básica');
          analysis = this.generateFallbackAnalysis(validated, data.gameName);
        }
        
        if (!analysis.success) {
          throw new Error(`Falha no processamento de ${data.gameName}: ${analysis.error}`);
        }
        analyses.push({ ...analysis, gameName: data.gameName });
      }

      const crossMetrics = this.aggregateCrossMetrics(analyses);
      const aiResult = await this.callAIAPI(
        { childId, games: analyses.map(a => a.gameName), childName: gameData[0]?.metrics.childName },
        { analyses: crossMetrics }
      );
      const report = await this.formatCrossGameReport(aiResult.text, crossMetrics, gameData[0]?.metrics.childName);

      // Salvar no banco se o databaseService estiver disponível
      if (this.databaseService && typeof this.databaseService.store === 'function') {
        try {
          await this.databaseService.store('cross_game_analysis', {
            childId,
            timestamp: new Date().toISOString(),
            analyses,
            crossReport: report,
            crossMetrics,
            aiConfidence: aiResult.confidence
          });
        } catch (error) {
          this.logger.warn('⚠️ Erro ao salvar análise cruzada no banco, continuando sem persistência', { error: error.message });
        }
      }

      this.logger.success('🌟 Análise cruzada gerada com sucesso', {
        childId,
        games: analyses.map(a => a.gameName)
      });
      return {
        success: true,
        report,
        crossMetrics,
        aiConfidence: aiResult.confidence,
        metadata: aiResult.metadata
      };
    } catch (error) {
      this.logger.error('❌ Erro na análise cruzada', { childId, error: error.message });
      return {
        success: false,
        error: error.message,
        report: 'Não foi possível gerar a análise cruzada. Tente novamente.',
        crossMetrics: {},
        aiConfidence: 0,
        metadata: {}
      };
    }
  }

  /**
   * Agrega métricas para análise cruzada
   * @param {Array} analyses - Análises de jogos
   * @returns {Object} Métricas agregadas
   * @private
   */
  aggregateCrossMetrics(analyses) {
    try {
      const domains = {};
      analyses.forEach(analysis => {
        const gameName = analysis.gameName;
        const cognitiveDomains = this.cognitiveDomains[gameName] || [];
        cognitiveDomains.forEach(domain => {
          domains[domain] = domains[domain] || { score: 0, count: 0 };
          domains[domain].score += analysis.specificAnalysis?.baseMetrics?.accuracy || 0;
          domains[domain].count += 1;
        });
      });

      const aggregated = {};
      Object.keys(domains).forEach(domain => {
        aggregated[domain] = domains[domain].score / domains[domain].count;
      });

      return {
        cognitiveDomains: aggregated,
        overallPerformance: analyses.reduce((sum, a) => sum + (a.specificAnalysis?.baseMetrics?.accuracy || 0), 0) / analyses.length,
        gameCount: analyses.length
      };
    } catch (error) {
      this.logger.error('❌ Erro ao agregar métricas cruzadas', { error: error.message });
      return { cognitiveDomains: {}, overallPerformance: 0, gameCount: 0 };
    }
  }

  /**
   * Formata relatório cruzado para pais
   * @param {string} aiText - Texto gerado pela IA
   * @param {Object} crossMetrics - Métricas cruzadas
   * @param {string} [childName] - Nome da criança
   * @returns {Object} Relatório formatado
   * @private
   */
  async formatCrossGameReport(aiText, crossMetrics, childName = 'sua criança') {
    try {
      const domains = Object.entries(crossMetrics.cognitiveDomains).map(([domain, score]) => ({
        domain: this.translateDomains([domain])[0],
        score
      }));
      const topDomains = domains.filter(d => d.score > 0.7).map(d => d.domain);
      const weakDomains = domains.filter(d => d.score < 0.5).map(d => d.domain);
      const visualData = this.generateVisualData({ specificAnalysis: { baseMetrics: { accuracy: crossMetrics.overallPerformance } } });

      return {
        summary: aiText || `${childName} está explorando várias aventuras e aprendendo muito!`,
        strengths: topDomains.length > 0 ? topDomains : ['Engajamento em várias atividades'],
        weaknesses: weakDomains.length > 0 ? weakDomains : ['Nenhuma área crítica'],
        activities: this.generateActivitySuggestions('cross', weakDomains, childName),
        visualData,
        overallScore: crossMetrics.overallPerformance,
        gameCount: crossMetrics.gameCount,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('❌ Erro ao formatar relatório cruzado', { error: error.message });
      return {
        summary: 'Não foi possível gerar o relatório cruzado.',
        strengths: [],
        weaknesses: [],
        activities: [],
        visualData: {},
        overallScore: 0,
        gameCount: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Analisa o progresso da criança ao longo do tempo
   * @param {string} childId - ID da criança
   * @param {Object} options - Opções de análise (e.g., período)
   * @returns {Promise<Object>} Relatório de progresso
   */
  async analyzeProgressOverTime(childId, options = {}) {
    try {
      if (!childId) {
        throw new Error('childId é obrigatório');
      }

      const { startDate, endDate, childName } = options;
      
      // Cache key para análise de progresso
      const cacheKey = `progress_analysis_${childId}_${startDate || 'all'}_${endDate || 'all'}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached) {
        this.logger.info('📥 Análise de progresso obtida do cache', { childId, cacheKey });
        return cached;
      }

      const query = { childId };
      if (startDate) query['timestamp[$gte]'] = new Date(startDate).toISOString();
      if (endDate) query['timestamp[$lte]'] = new Date(endDate).toISOString();

      const pastAnalyses = await this.databaseService.query('ai_analysis', query);
      if (pastAnalyses.length === 0) {
        throw new Error('Nenhum dado histórico encontrado para a criança');
      }

      const progress = this.calculateProgress(pastAnalyses);
      const aiResult = await this.callAIAPI(
        { childId, childName, analyses: pastAnalyses },
        { progress }
      );
      const report = await this.formatProgressReport(aiResult.text, progress, childName);

      const result = {
        success: true,
        report,
        progress,
        aiConfidence: aiResult.confidence,
        metadata: aiResult.metadata
      };

      // Armazenar no cache com tags para invalidação
      this.cache.set(cacheKey, result, {
        tags: [`child_${childId}`, 'progress_analysis', `date_${new Date().toISOString().split('T')[0]}`],
        ttl: 3600000 // 1 hora para análises de progresso
      });

      await this.databaseService.store('progress_analysis', {
        childId,
        timestamp: new Date().toISOString(),
        predictionReport: report,
        pastAnalyses,
        aiConfidence: aiResult.confidence
      });

      this.logger.success('🌟 Relatório de progresso gerado', { childId, period: { startDate, endDate } });
      return result;
    } catch (error) {
      this.logger.error('❌ Erro na análise de progresso', { childId, error: error.message });
      return {
        success: false,
        error: error.message,
        report: 'Não foi possível gerar o relatório de progresso.',
        progress: {},
        aiConfidence: 0,
        metadata: {}
      };
    }
  }

  /**
   * Calcula progresso com base em análises passadas
   * @param {Array} pastAnalyses - Análises anteriores
   * @returns {Object} Dados de progresso
   * @private
   */
  calculateProgress(pastAnalyses) {
    try {
      const sortedAnalyses = pastAnalyses.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      const domains = {};

      sortedAnalyses.forEach(analysis => {
        const gameName = analysis.gameName;
        const cognitiveDomains = this.cognitiveDomains[gameName] || [];
        cognitiveDomains.forEach(domain => {
          domains[domain] = domains[domain] || [];
          domains[domain].push({
            timestamp: analysis.timestamp,
            accuracy: analysis.systemAnalysis?.specificAnalysis?.baseMetrics?.accuracy || 0
          });
        });
      });

      const progress = {};
      Object.keys(domains).forEach(domain => {
        const scores = domains[domain];
        if (scores.length > 1) {
          const initial = scores[0].accuracy;
          const final = scores[scores.length - 1].accuracy;
          progress[domain] = {
            improvement: ((final - initial) / (initial || 1)) * 100,
            trend: final > initial ? 'melhora' : final < initial ? 'declínio' : 'estável'
          };
        }
      });

      return {
        domains: progress,
        sessionCount: sortedAnalyses.length,
        earliest: sortedAnalyses[0].timestamp,
        latest: sortedAnalyses[sortedAnalyses.length - 1].timestamp
      };
    } catch (error) {
      this.logger.error('❌ Erro ao calcular progresso', { error: error.message });
      return { domains: {}, sessionCount: 0 };
    }
  }

  /**
   * Formata relatório de progresso para pais
   * @param {string} aiText - Texto gerado pela IA
   * @param {Object} progress - Dados de progresso
   * @param {string} [childName] - Nome da criança
   * @returns {Object} Relatório formatado
   * @private
   */
  async formatProgressReport(aiText, progress, childName = 'sua criança') {
    try {
      const improvements = Object.entries(progress.domains)
        .filter(([_, data]) => data.improvement > 0)
        .map(([domain, data]) => ({ domain: this.translateDomains([domain])[0], improvement: data.improvement }));
      const declines = Object.entries(progress.domains)
        .filter(([_, data]) => data.improvement < 0)
        .map(([domain, data]) => ({ domain: this.translateDomains([domain])[0], improvement: data.improvement }));
      const visualData = {
        improvements: improvements.map(i => ({ label: i.domain, value: i.improvement, color: '#4CAF50' })),
        declines: declines.map(d => ({ label: d.domain, value: Math.abs(d.improvement), color: '#F44336' }))
      };

      return {
        summary: aiText || `${childName} está crescendo e aprendendo! Veja como ela evoluiu com o tempo.`,
        improvements: improvements.length > 0 ? improvements : [{ domain: 'Engajamento geral', improvement: 0 }],
        declines: declines.length > 0 ? declines : [],
        activities: this.generateActivitySuggestions('cross', declines.map(d => d.domain), childName),
        visualData,
        sessionCount: progress.sessionCount,
        period: { start: progress.earliest, end: progress.latest },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('❌ Erro ao formatar relatório de progresso', { error: error.message });
      return {
        summary: 'Não foi possível gerar o relatório de progresso.',
        improvements: [],
        declines: [],
        activities: [],
        visualData: {},
        sessionCount: 0,
        period: {},
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Personaliza relatório com base em preferências
   * @param {Object} report - Relatório original
   * @param {Object} preferences - Preferências (e.g., tom, nível de detalhe)
   * @returns {Promise<Object>} Relatório personalizado
   */
  async customizeReport(report, preferences = {}) {
    try {
      const { tone = 'motivador', detailLevel = 'simples', childName = 'sua criança' } = preferences;
      const prompt = `
Personalize o seguinte relatório para pais, ajustando o tom para "${tone}" (opções: motivador, neutro, detalhado) e o nível de detalhe para "${detailLevel}" (opções: simples, moderado, completo). Use o nome "${childName}" e mantenha a linguagem acessível, em português, sem jargões técnicos. Retorne apenas o texto do relatório personalizado.

**Relatório Original:**
${JSON.stringify(report, null, 2)}

**Instruções:**
- Para tom "motivador", use frases encorajadoras e metáforas (e.g., "${childName} é como um super-herói").
- Para tom "neutro", seja direto e objetivo.
- Para tom "detalhado", inclua mais contexto sobre o progresso.
- Para nível "simples", resuma em poucas frases.
- Para nível "moderado", inclua detalhes principais.
- Para nível "completo", adicione exemplos e explicações.
`;

      const aiResult = await this.callAIAPI({}, { report });
      return {
        ...report,
        summary: aiText || report.summary,
        customized: true,
        preferences,
        childName
      };
    } catch (error) {
      this.logger.error('❌ Erro ao personalizar relatório', { error: error.message });
      return { ...report, customized: false };
    }
  }

  /**
   * Registra feedback de atividades sugeridas
   * @param {string} childId - ID da criança
   * @param {string} activity - Atividade sugerida
   * @param {Object} feedback - Feedback (e.g., { tested: true, success: true })
   * @returns {Promise<void>}
   */
  async registerActivityFeedback(childId, activity, feedback) {
    try {
      if (!childId || !activity || !feedback) {
        throw new Error('childId, activity e feedback são obrigatórios');
      }

      const key = `${childId}-${activity.gameName || 'cross'}`;
      const currentFeedback = this.feedbackHistory.get(key) || {};
      this.feedbackHistory.set(key, { ...currentFeedback, [activity]: feedback });

      await this.databaseService.store('activity_feedback', {
        childId,
        activity,
        feedback,
        timestamp: new Date().toISOString()
      });

      this.logger.success('🌟 Feedback de atividade registrado', { childId, activity });
    } catch (error) {
      this.logger.error('❌ Erro ao registrar feedback', { childId, activity, error: error.message });
    }
  }

  /**
   * Prevê trajetória de desenvolvimento
   * @param {string} childId - ID da criança
   * @param {Object} options - Opções (e.g., horizonte de previsão)
   * @returns {Promise<Object>} Previsão de desenvolvimento
   */
  async predictDevelopmentTrajectory(childId, options = {}) {
    try {
      if (!childId) {
        throw new Error('childId é obrigatório');
      }

      const { horizonMonths = 3, childName } = options;
      const pastAnalyses = await this.databaseService.query('ai_analysis', { childId });
      if (pastAnalyses.length < 2) {
        throw new Error('Dados insuficientes para previsão');
      }

      const progress = this.calculateProgress(pastAnalyses);
      const prediction = this.calculatePrediction(progress, horizonMonths);
      const aiResult = await this.callAIAPI(
        { childId, childName, analyses: pastAnalyses },
        { prediction }
      );
      const report = await this.formatPredictionReport(aiResult.text, prediction, childName);

      await this.databaseService.store('prediction_analysis', {
        childId,
        timestamp: new Date().toISOString(),
        predictionReport: report,
        pastAnalyses,
        aiConfidence: aiResult.confidence
      });

      this.logger.success('🌟 Previsão de desenvolvimento gerada', { childId, horizonMonths });
      return {
        success: true,
        report,
        prediction,
        aiConfidence: aiResult.confidence,
        metadata: aiResult.metadata
      };
    } catch (error) {
      this.logger.error('❌ Erro na previsão de desenvolvimento', { childId, error: error.message });
      return {
        success: false,
        error: error.message,
        report: 'Não foi possível gerar a previsão de desenvolvimento.',
        prediction: {},
        aiConfidence: 0,
        metadata: {}
      };
    }
  }

  /**
   * Calcula previsão de desenvolvimento
   * @param {Object} progress - Dados de progresso
   * @param {number} horizonMonths - Horizonte de previsão em meses
   * @returns {Object} Previsão
   * @private
   */
  calculatePrediction(progress, horizonMonths) {
    try {
      const predictions = {};
      Object.entries(progress.domains).forEach(([domain, data]) => {
        const currentScore = data.improvement / 100;
        const projectedScore = Math.min(1, currentScore + (currentScore * 0.1 * horizonMonths)); // Crescimento de 10% por mês
        predictions[domain] = {
          projectedScore,
          confidence: data.trend === 'melhora' ? 0.9 : data.trend === 'estável' ? 0.7 : 0.5
        };
      });

      return {
        domains: predictions,
        horizonMonths,
        overallConfidence: Object.values(predictions).reduce((sum, p) => sum + p.confidence, 0) / Object.keys(predictions).length
      };
    } catch (error) {
      this.logger.error('❌ Erro ao calcular previsão', { error: error.message });
      return { domains: {}, horizonMonths, overallConfidence: 0 };
    }
  }

  /**
   * Formata relatório de previsão para pais
   * @param {string} aiText - Texto gerado pela IA
   * @param {Object} prediction - Dados de previsão
   * @param {string} [childName] - Nome da criança
   * @returns {Object} Relatório formatado
   * @private
   */
  async formatPredictionReport(aiText, prediction, childName = 'sua criança') {
    try {
      const highPotential = Object.entries(prediction.domains)
        .filter(([_, data]) => data.projectedScore > 0.8)
        .map(([domain, data]) => ({ domain: this.translateDomains([domain])[0], projectedScore: data.projectedScore }));
      const needsAttention = Object.entries(prediction.domains)
        .filter(([_, data]) => data.projectedScore < 0.6)
        .map(([domain, data]) => ({ domain: this.translateDomains([domain])[0], projectedScore: data.projectedScore }));

      return {
        summary: aiText || `${childName} tem um futuro brilhante! Veja onde ela pode chegar com prática.`,
        highPotential: highPotential.length > 0 ? highPotential : [{ domain: 'Engajamento geral', projectedScore: 0.5 }],
        needsAttention: needsAttention.length > 0 ? needsAttention : [],
        activities: this.generateActivitySuggestions('cross', needsAttention.map(n => n.domain), childName),
        visualData: {
          predictions: highPotential.concat(needsAttention).map(p => ({
            label: p.domain,
            value: p.projectedScore,
            color: p.projectedScore > 0.8 ? '#4CAF50' : '#FF9800'
          }))
        },
        horizonMonths: prediction.horizonMonths,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('❌ Erro ao formatar relatório de previsão', { error: error.message });
      return {
        summary: 'Não foi possível gerar o relatório de previsão.',
        highPotential: [],
        needsAttention: [],
        activities: [],
        visualData: {},
        horizonMonths: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Gera relatório personalizado baseado nos dados da criança
   * @param {string} childId - ID da criança
   * @param {Object} options - Opções do relatório
   * @returns {Promise<Object>} Relatório personalizado
   */
  async generatePersonalizedReport(childId, options = {}) {
    try {
      this.logger.info(`🎯 Gerando relatório personalizado para criança: ${childId}`);
      
      const {
        sessions = [],
        metrics = [],
        parentId,
        reportType = 'comprehensive',
        language = 'pt-BR',
        focus = 'development'
      } = options;

      // Consolidar dados das sessões
      const consolidatedData = await this.consolidateSessionData(sessions, metrics);
      
      // Gerar análise específica baseada no tipo de relatório
      let reportData;
      
      switch (reportType) {
        case 'progress':
          reportData = await this.analyzeProgressOverTime(childId, {
            sessions: consolidatedData,
            focus: focus,
            language: language
          });
          break;
        case 'comprehensive':
          reportData = await this.generateComprehensiveReport(childId, consolidatedData, options);
          break;
        case 'cognitive':
          reportData = await this.generateCognitiveReport(childId, consolidatedData, options);
          break;
        case 'behavioral':
          reportData = await this.generateBehavioralReport(childId, consolidatedData, options);
          break;
        default:
          reportData = await this.generateComprehensiveReport(childId, consolidatedData, options);
      }

      // Personalizar relatório com preferências
      const personalizedReport = await this.customizeReport(reportData, {
        parentId: parentId,
        language: language,
        focus: focus,
        reportType: reportType
      });

      return {
        id: `report_${childId}_${Date.now()}`,
        childId: childId,
        parentId: parentId,
        type: reportType,
        language: language,
        focus: focus,
        generatedAt: new Date().toISOString(),
        ...personalizedReport
      };
    } catch (error) {
      this.logger.error(`❌ Erro ao gerar relatório personalizado para ${childId}:`, error);
      throw new Error(`Falha na geração do relatório: ${error.message}`);
    }
  }

  /**
   * Consolida dados das sessões para análise
   * @param {Array} sessions - Dados das sessões
   * @param {Array} metrics - Métricas consolidadas
   * @returns {Promise<Object>} Dados consolidados
   * @private
   */
  async consolidateSessionData(sessions, metrics) {
    try {
      const consolidated = {
        totalSessions: sessions.length,
        totalMetrics: metrics.length,
        games: new Set(),
        cognitiveMetrics: [],
        behavioralMetrics: [],
        progressMetrics: [],
        timeRange: {
          start: null,
          end: null
        }
      };

      // Processar sessões
      sessions.forEach(session => {
        if (session.gameName) {
          consolidated.games.add(session.gameName);
        }
        if (session.timestamp) {
          const timestamp = new Date(session.timestamp);
          if (!consolidated.timeRange.start || timestamp < consolidated.timeRange.start) {
            consolidated.timeRange.start = timestamp;
          }
          if (!consolidated.timeRange.end || timestamp > consolidated.timeRange.end) {
            consolidated.timeRange.end = timestamp;
          }
        }
      });

      // Processar métricas
      metrics.forEach(metric => {
        if (metric.cognitive) {
          consolidated.cognitiveMetrics.push(metric);
        }
        if (metric.behavioral) {
          consolidated.behavioralMetrics.push(metric);
        }
        if (metric.progress) {
          consolidated.progressMetrics.push(metric);
        }
      });

      consolidated.games = Array.from(consolidated.games);
      
      return consolidated;
    } catch (error) {
      this.logger.error('❌ Erro ao consolidar dados das sessões:', error);
      return {
        totalSessions: 0,
        totalMetrics: 0,
        games: [],
        cognitiveMetrics: [],
        behavioralMetrics: [],
        progressMetrics: [],
        timeRange: { start: null, end: null }
      };
    }
  }

  /**
   * Gera relatório compreensivo
   * @param {string} childId - ID da criança
   * @param {Object} consolidatedData - Dados consolidados
   * @param {Object} options - Opções do relatório
   * @returns {Promise<Object>} Relatório compreensivo
   * @private
   */
  async generateComprehensiveReport(childId, consolidatedData, options = {}) {
    try {
      // Analisar progresso ao longo do tempo
      const progressAnalysis = await this.analyzeProgressOverTime(childId, {
        sessions: consolidatedData,
        focus: 'comprehensive'
      });

      // Analisar padrões cognitivos
      const cognitiveAnalysis = await this.analyzeCognitivePatterns(consolidatedData);

      // Analisar padrões comportamentais
      const behavioralAnalysis = await this.analyzeBehavioralPatterns(consolidatedData);

      // Gerar predições
      const predictions = await this.predictDevelopmentTrajectory(childId, {
        data: consolidatedData,
        horizonMonths: 3
      });

      return {
        overview: {
          totalSessions: consolidatedData.totalSessions,
          gamesPlayed: consolidatedData.games.length,
          timeRange: consolidatedData.timeRange,
          overallProgress: progressAnalysis.overallProgress || 0.5
        },
        cognitive: cognitiveAnalysis,
        behavioral: behavioralAnalysis,
        progress: progressAnalysis,
        predictions: predictions,
        recommendations: this.generateRecommendations(
          cognitiveAnalysis,
          behavioralAnalysis,
          progressAnalysis
        )
      };
    } catch (error) {
      this.logger.error('❌ Erro ao gerar relatório compreensivo:', error);
      throw error;
    }
  }

  /**
   * Gera relatório cognitivo
   * @param {string} childId - ID da criança
   * @param {Object} consolidatedData - Dados consolidados
   * @param {Object} options - Opções do relatório
   * @returns {Promise<Object>} Relatório cognitivo
   * @private
   */
  async generateCognitiveReport(childId, consolidatedData, options = {}) {
    try {
      const cognitiveAnalysis = await this.analyzeCognitivePatterns(consolidatedData);
      const cognitiveProgress = await this.analyzeCognitiveProgress(consolidatedData);

      return {
        overview: {
          focusArea: 'cognitive',
          totalCognitiveMetrics: consolidatedData.cognitiveMetrics.length,
          cognitiveScore: cognitiveAnalysis.overallScore || 0.5
        },
        analysis: cognitiveAnalysis,
        progress: cognitiveProgress,
        recommendations: this.generateCognitiveRecommendations(cognitiveAnalysis)
      };
    } catch (error) {
      this.logger.error('❌ Erro ao gerar relatório cognitivo:', error);
      throw error;
    }
  }

  /**
   * Gera relatório comportamental
   * @param {string} childId - ID da criança
   * @param {Object} consolidatedData - Dados consolidados
   * @param {Object} options - Opções do relatório
   * @returns {Promise<Object>} Relatório comportamental
   * @private
   */
  async generateBehavioralReport(childId, consolidatedData, options = {}) {
    try {
      const behavioralAnalysis = await this.analyzeBehavioralPatterns(consolidatedData);
      const behavioralProgress = await this.analyzeBehavioralProgress(consolidatedData);

      return {
        overview: {
          focusArea: 'behavioral',
          totalBehavioralMetrics: consolidatedData.behavioralMetrics.length,
          behavioralScore: behavioralAnalysis.overallScore || 0.5
        },
        analysis: behavioralAnalysis,
        progress: behavioralProgress,
        recommendations: this.generateBehavioralRecommendations(behavioralAnalysis)
      };
    } catch (error) {
      this.logger.error('❌ Erro ao gerar relatório comportamental:', error);
      throw error;
    }
  }

  /**
   * Analisa padrões cognitivos
   * @param {Object} consolidatedData - Dados consolidados
   * @returns {Promise<Object>} Análise cognitiva
   * @private
   */
  async analyzeCognitivePatterns(consolidatedData) {
    try {
      const cognitiveMetrics = consolidatedData.cognitiveMetrics || [];
      
      if (cognitiveMetrics.length === 0) {
        return {
          overallScore: 0.5,
          patterns: [],
          strengths: [],
          weaknesses: [],
          domains: {}
        };
      }

      // Análise por domínios cognitivos
      const domains = {};
      COGNITIVE_DOMAINS.forEach(domain => {
        domains[domain] = {
          score: 0.5,
          count: 0,
          trend: 'stable'
        };
      });

      // Processar métricas cognitivas
      cognitiveMetrics.forEach(metric => {
        if (metric.cognitive && metric.cognitive.domains) {
          Object.entries(metric.cognitive.domains).forEach(([domain, score]) => {
            if (domains[domain]) {
              domains[domain].score += score;
              domains[domain].count++;
            }
          });
        }
      });

      // Calcular médias
      Object.keys(domains).forEach(domain => {
        if (domains[domain].count > 0) {
          domains[domain].score = domains[domain].score / domains[domain].count;
        }
      });

      const overallScore = Object.values(domains).reduce((sum, domain) => sum + domain.score, 0) / Object.keys(domains).length;
      const strengths = Object.entries(domains).filter(([_, data]) => data.score > 0.7).map(([domain]) => domain);
      const weaknesses = Object.entries(domains).filter(([_, data]) => data.score < 0.5).map(([domain]) => domain);

      return {
        overallScore,
        patterns: this.identifyCognitivePatterns(domains),
        strengths,
        weaknesses,
        domains
      };
    } catch (error) {
      this.logger.error('❌ Erro ao analisar padrões cognitivos:', error);
      return {
        overallScore: 0.5,
        patterns: [],
        strengths: [],
        weaknesses: [],
        domains: {}
      };
    }
  }

  /**
   * Analisa padrões comportamentais
   * @param {Object} consolidatedData - Dados consolidados
   * @returns {Promise<Object>} Análise comportamental
   * @private
   */
  async analyzeBehavioralPatterns(consolidatedData) {
    try {
      const behavioralMetrics = consolidatedData.behavioralMetrics || [];
      
      if (behavioralMetrics.length === 0) {
        return {
          overallScore: 0.5,
          patterns: [],
          strengths: [],
          concerns: [],
          engagement: 0.5
        };
      }

      // Análise de engajamento ao longo do tempo
      let totalEngagement = 0;
      let engagementCount = 0;

      behavioralMetrics.forEach(metric => {
        if (metric.behavioral && metric.behavioral.engagement) {
          totalEngagement += metric.behavioral.engagement;
          engagementCount++;
        }
      });

      const engagement = engagementCount > 0 ? totalEngagement / engagementCount : 0.5;
      const overallScore = engagement; // Simplificado para este exemplo

      return {
        overallScore,
        patterns: this.identifyBehavioralPatterns(behavioralMetrics),
        strengths: engagement > 0.7 ? ['Alto engajamento'] : [],
        concerns: engagement < 0.5 ? ['Baixo engajamento'] : [],
        engagement
      };
    } catch (error) {
      this.logger.error('❌ Erro ao analisar padrões comportamentais:', error);
      return {
        overallScore: 0.5,
        patterns: [],
        strengths: [],
        concerns: [],
        engagement: 0.5
      };
    }
  }

  /**
   * Analisa progresso cognitivo
   * @param {Object} consolidatedData - Dados consolidados
   * @returns {Promise<Object>} Progresso cognitivo
   * @private
   */
  async analyzeCognitiveProgress(consolidatedData) {
    try {
      const cognitiveMetrics = consolidatedData.cognitiveMetrics || [];
      
      if (cognitiveMetrics.length < 2) {
        return {
          trend: 'insufficient_data',
          improvement: 0,
          milestones: []
        };
      }

      // Ordenar métricas por timestamp
      const sortedMetrics = cognitiveMetrics.sort((a, b) => 
        new Date(a.timestamp) - new Date(b.timestamp)
      );
      const firstMetric = sortedMetrics[0];
      const lastMetric = sortedMetrics[sortedMetrics.length - 1];

      // Calcular melhoria
      const improvement = lastMetric.overallScore - firstMetric.overallScore;
      const trend = improvement > 0.1 ? 'improving' : improvement < -0.1 ? 'declining' : 'stable';

      return {
        trend,
        improvement,
        milestones: this.identifyMilestones(sortedMetrics)
      };
    } catch (error) {
      this.logger.error('❌ Erro ao analisar progresso cognitivo:', error);
      return {
        trend: 'unknown',
        improvement: 0,
        milestones: []
      };
    }
  }

  /**
   * Analisa progresso comportamental
   * @param {Object} consolidatedData - Dados consolidados
   * @returns {Promise<Object>} Progresso comportamental
   * @private
   */
  async analyzeBehavioralProgress(consolidatedData) {
    try {
      const behavioralMetrics = consolidatedData.behavioralMetrics || [];
      
      if (behavioralMetrics.length < 2) {
        return {
          trend: 'insufficient_data',
          improvement: 0,
          behaviors: []
        };
      }

      // Análise simplificada de progresso comportamental
      const recent = behavioralMetrics.slice(-5); // Últimas 5 métricas
      const older = behavioralMetrics.slice(0, 5); // Primeiras 5 métricas

      const recentAvg = recent.reduce((sum, m) => sum + (m.behavioral?.engagement || 0.5), 0) / recent.length;
      const olderAvg = older.reduce((sum, m) => sum + (m.behavioral?.engagement || 0.5), 0) / older.length;

      const improvement = recentAvg - olderAvg;
      const trend = improvement > 0.1 ? 'improving' : improvement < 0.1 ? 'declining' : 'stable';

      return {
        trend,
        improvement,
        behaviors: this.identifyBehavioralChanges(behavioralMetrics)
      };
    } catch (error) {
      this.logger.error('❌ Erro ao analisar progresso comportamental:', error);
      return {
        trend: 'unknown',
        improvement: 0,
        behaviors: []
      };
    }
  }

  /**
   * Identifica padrões cognitivos
   * @param {Object} domains - Domínios cognitivos
   * @returns {Array} Padrões identificados
   * @private
   */
  identifyCognitivePatterns(domains) {
    const patterns = [];
    
    // Identificar padrões baseados nos scores dos domínios
    const domainScores = Object.entries(domains).map(([domain, data]) => ({
      domain,
      score: data.score
    }));

    // Padrão de fortaleza
    const strongDomains = domainScores.filter(d => d.score > 0.8);
    if (strongDomains.length > 0) {
      patterns.push({
        type: 'strength',
        description: `Forte desempenho em ${strongDomains.map(d => d.domain).join(', ')}`,
        domains: strongDomains.map(d => d.domain)
      });
    }

    // Padrão de consistência
    const scores = domainScores.map(d => d.score);
    const variance = this.calculateVariance(scores);
    if (variance < 0.1) {
      patterns.push({
        type: 'consistency',
        description: 'Desempenho consistente em todos os domínios',
        variance
      });
    }

    return patterns;
  }

  /**
   * Identifica padrões comportamentais
   * @param {Array} behavioralMetrics - Métricas comportamentais
   * @returns {Array} Padrões identificados
   * @private
   */
  identifyBehavioralPatterns(behavioralMetrics) {
    const patterns = [];
    
    // Análise de engajamento ao longo do tempo
    const engagementValues = behavioralMetrics.map(m => m.behavioral?.engagement || 0.5);
    const avgEngagement = engagementValues.reduce((sum, val) => sum + val, 0) / engagementValues.length;

    if (avgEngagement > 0.8) {
      patterns.push({
        type: 'high_engagement',
        description: 'Demonstra alto engajamento nas atividades',
        value: avgEngagement
      });
    }

    // Análise de consistência comportamental
    const variance = this.calculateVariance(engagementValues);
    if (variance < 0.1) {
      patterns.push({
        type: 'consistent_behavior',
        description: 'Comportamento consistente ao longo das sessões',
        variance
      });
    }

    return patterns;
  }

  /**
   * Identifica marcos de desenvolvimento
   * @param {Array} sortedMetrics - Métricas ordenadas por tempo
   * @returns {Array} Marcos identificados
   * @private
   */
  identifyMilestones(sortedMetrics) {
    const milestones = [];
    
    // Identificar melhorias significativas
    for (let i = 1; i < sortedMetrics.length; i++) {
      const previous = sortedMetrics[i - 1];
      const current = sortedMetrics[i];
      
      if (current.overallScore - previous.overallScore > 0.2) {
        milestones.push({
          type: 'improvement',
          description: 'Melhoria significativa no desempenho',
          date: current.timestamp,
          improvement: current.overallScore - previous.overallScore
        });
      }
    }

    return milestones;
  }



  /**
   * Identifica mudanças comportamentais
   * @param {Array} behavioralMetrics - Métricas comportamentais
   * @returns {Array} Mudanças identificadas
   * @private
   */
  identifyBehavioralChanges(behavioralMetrics) {
    const changes = [];
    
    // Análise simplificada de mudanças
    const firstHalf = behavioralMetrics.slice(0, Math.floor(behavioralMetrics.length / 2));
    const secondHalf = behavioralMetrics.slice(Math.floor(behavioralMetrics.length / 2));

    const firstAvg = firstHalf.reduce((sum, m) => sum + (m.behavioral?.engagement || 0.5), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, m) => sum + (m.behavioral?.engagement || 0.5), 0) / secondHalf.length;

    if (secondAvg > firstAvg + 0.2) {
      changes.push({
        type: 'engagement_improvement',
        description: 'Melhoria no engajamento ao longo do tempo',
        change: secondAvg - firstAvg
      });
    }

    return changes;
  }

  /**
   * Gera recomendações baseadas nas análises
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @param {Object} behavioralAnalysis - Análise comportamental
   * @param {Object} progressAnalysis - Análise de progresso
   * @returns {Array} Recomendações
   * @private
   */
  generateRecommendations(cognitiveAnalysis, behavioralAnalysis, progressAnalysis) {
    const recommendations = [];

    // Recomendações baseadas em fortalezas cognitivas
    if (cognitiveAnalysis.strengths && cognitiveAnalysis.strengths.length > 0) {
      recommendations.push({
        type: 'strength_building',
        description: `Continue fortalecendo: ${cognitiveAnalysis.strengths.join(', ')}`,
        priority: 'high',
        area: 'cognitive'
      });
    }

    // Recomendações baseadas em fraquezas cognitivas
    if (cognitiveAnalysis.weaknesses && cognitiveAnalysis.weaknesses.length > 0) {
      recommendations.push({
        type: 'improvement_needed',
        description: `Foque em melhorar: ${cognitiveAnalysis.weaknesses.join(', ')}`,
        priority: 'medium',
        area: 'cognitive'
      });
    }

    // Recomendações comportamentais
    if (behavioralAnalysis.engagement && behavioralAnalysis.engagement < 0.6) {
      recommendations.push({
        type: 'engagement_boost',
        description: 'Considere atividades mais interativas para aumentar o engajamento',
        priority: 'high',
        area: 'behavioral'
      });
    }

    // Recomendações de progresso
    if (progressAnalysis.trend === 'improving') {
      recommendations.push({
        type: 'maintain_progress',
        description: 'Continue com as estratégias atuais - o progresso é consistente',
        priority: 'medium',
        area: 'progress'
      });
    }

    return recommendations;
  }

  /**
   * Gera recomendações cognitivas específicas
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Array} Recomendações cognitivas
   * @private
   */
  generateCognitiveRecommendations(cognitiveAnalysis) {
    const recommendations = [];

    // Recomendações baseadas em domínios específicos
    if (cognitiveAnalysis.domains) {
      Object.entries(cognitiveAnalysis.domains).forEach(([domain, data]) => {
        if (data.score < 0.5) {
          recommendations.push({
            type: 'domain_focus',
            description: `Trabalhe mais atividades de ${domain}`,
            priority: 'high',
            domain: domain,
            currentScore: data.score
          });
        }
      });
    }

    return recommendations;
  }

  /**
   * Gera recomendações comportamentais específicas
   * @param {Object} behavioralAnalysis - Análise comportamental
   * @returns {Array} Recomendações comportamentais
   * @private
   */
  generateBehavioralRecommendations(behavioralAnalysis) {
    const recommendations = [];

    // Recomendações baseadas em engajamento
    if (behavioralAnalysis.engagement < 0.6) {
      recommendations.push({
        type: 'engagement_strategies',
        description: 'Experimente atividades mais curtas e variadas',
        priority: 'high',
        currentEngagement: behavioralAnalysis.engagement
      });
    }

    // Recomendações baseadas em padrões identificados
    if (behavioralAnalysis.patterns) {
      behavioralAnalysis.patterns.forEach(pattern => {
        if (pattern.type === 'low_engagement') {
          recommendations.push({
            type: 'pattern_intervention',
            description: 'Considere pausas mais frequentes durante as atividades',
            priority: 'medium',
            pattern: pattern.type
          });
        }
      });
    }

    return recommendations;
  }

  /**
   * Calcula variância de um array de valores
   * @param {Array} values - Valores para calcular variância
   * @returns {number} Variância
   * @private
   */
  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }

  /**
   * Carrega o database service dinamicamente quando necessário
   * @returns {Promise<Object>} Database service
   * @private
   */
  async getDatabaseService() {
    if (this.databaseService) {
      return this.databaseService;
    }

    // Tentar carregar dinamicamente apenas no backend
    if (isNode) {
      try {
        // Verificar se estamos no contexto do frontend build ou backend
        if (typeof window !== 'undefined') {
          throw new Error('Database não disponível no frontend');
        }
        
        const { default: databaseSingleton } = await import('../../../../database/services/databaseInstance.js');
        this.databaseService = databaseSingleton;
        return this.databaseService;
      } catch (error) {
        this.logger.warn('Database service não disponível:', error.message);
        // Retornar mock para não quebrar o frontend
        return {
          store: async () => ({ success: true }),
          query: async () => ([]),
          isConnected: () => false
        };
      }
    } else {
      // No frontend, retornar mock
      return {
        store: async () => ({ success: true }),
        query: async () => ([]),
        isConnected: () => false
      };
    }
  }

  /**
   * Verifica se um jogo é suportado
   * @param {string} gameName - Nome do jogo
   * @returns {boolean} True se o jogo é suportado
   */
  isGameSupported(gameName) {
    return this.supportedGames.includes(gameName);
  }

  /**
   * Gera análise básica quando SystemOrchestrator não está disponível
   * @param {Object} validatedMetrics - Métricas validadas
   * @param {string} gameName - Nome do jogo
   * @returns {Object} Análise básica
   * @private
   */
  generateFallbackAnalysis(validatedMetrics, gameName) {
    const baseAnalysis = {
      success: true,
      gameName,
      childId: validatedMetrics.childId,
      sessionId: validatedMetrics.sessionId,
      timestamp: validatedMetrics.timestamp,
      mode: 'fallback',
      specificAnalysis: {
        strengths: ['Engajamento', 'Persistência'],
        weaknesses: ['Velocidade de resposta'],
        cognitiveScores: {
          attention: 0.75,
          memory: 0.70,
          processing: 0.65,
          executive: 0.72
        },
        therapeuticMetrics: {
          engagementLevel: 0.75,
          frustrationLevel: 0.25,
          progressRate: 0.70,
          adaptabilityScore: 0.68
        },
        gameSpecificMetrics: this.generateGameSpecificFallback(gameName, validatedMetrics)
      },
      analysis: {
        overall: 'Análise básica realizada com sucesso',
        cognitive: 'Dados processados sem SystemOrchestrator',
        therapeutic: 'Métricas extraídas das sessões de jogo',
        behavioral: 'Padrões identificados nos dados brutos'
      }
    };

    return baseAnalysis;
  }

  /**
   * Gera métricas específicas do jogo para análise fallback
   * @param {string} gameName - Nome do jogo
   * @param {Object} validatedMetrics - Métricas validadas
   * @returns {Object} Métricas específicas do jogo
   * @private
   */
  generateGameSpecificFallback(gameName, validatedMetrics) {
    const baseMetrics = {
      completionRate: 0.75,
      accuracyRate: 0.70,
      averageResponseTime: 2500,
      difficultyProgression: 0.65
    };

    // Adicionar métricas específicas baseadas no jogo
    switch (gameName) {
      case 'MemoryGame':
        return {
          ...baseMetrics,
          memoryCapacity: 6,
          sequenceLength: 4,
          visualProcessing: 0.72
        };
      case 'ColorMatch':
        return {
          ...baseMetrics,
          colorRecognition: 0.85,
          patternMatching: 0.68,
          visualAttention: 0.73
        };
      case 'ImageAssociation':
        return {
          ...baseMetrics,
          associativeMemory: 0.71,
          categoricalThinking: 0.74,
          semanticProcessing: 0.69
        };
      case 'LetterRecognition':
        return {
          ...baseMetrics,
          letterAccuracy: 0.78,
          phonemeRecognition: 0.72,
          readingReadiness: 0.70
        };
      case 'MusicalSequence':
        return {
          ...baseMetrics,
          auditoryMemory: 0.73,
          sequentialProcessing: 0.67,
          rhythmRecognition: 0.75
        };
      case 'PadroesVisuais':
        return {
          ...baseMetrics,
          patternRecognition: 0.76,
          visualProcessing: 0.71,
          spatialAwareness: 0.74
        };
      case 'QuebraCabeca':
        return {
          ...baseMetrics,
          spatialReasoning: 0.77,
          problemSolving: 0.73,
          persistence: 0.79
        };
      default:
        return baseMetrics;
    }
  }

  /**
   * Método público para processar apenas dados multissensoriais
   * @param {Object} multisensoryData - Dados dos sensores
   * @param {Object} basicGameMetrics - Métricas básicas do jogo
   * @returns {Promise<Object>} Análise multissensoriais
   * @public
   */
  async analyzeMultisensoryData(multisensoryData, basicGameMetrics = {}) {
    try {
      this.logger.info('🧠 Análise multissensorial independente iniciada', {
        sensorTypes: Object.keys(multisensoryData),
        hasGameMetrics: Object.keys(basicGameMetrics).length > 0
      });

      const analysis = await this.processMultisensoryData(multisensoryData, basicGameMetrics);
      
      if (analysis.success) {
        this.logger.success('🌟 Análise multissensorial concluída', {
          modalityPreferences: analysis.modalityPreferences?.primary,
          insights: analysis.insights?.strengths?.length || 0
        });
      }

      return analysis;
    } catch (error) {
      this.logger.error('❌ Erro na análise multissensorial independente', { error: error.message });
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Gera relatório de adaptações multissensoriais
   * @param {Object} multisensoryAnalysis - Análise multissensorial
   * @param {string} childName - Nome da criança
   * @returns {Object} Relatório de adaptações
   * @public
   */
  generateAdaptationReport(multisensoryAnalysis, childName = 'sua criança') {
    try {
      if (!multisensoryAnalysis || !multisensoryAnalysis.success) {
        return {
          success: false,
          error: 'Análise multissensorial não disponível'
        };
      }

      const { modalityPreferences, insights, sensorPatterns } = multisensoryAnalysis;
      const primaryModality = modalityPreferences?.primary || 'visual';
      const adaptations = insights?.adaptations || [];
      
      const report = {
        childName,
        primaryModality,
        profile: modalityPreferences?.profile || 'indefinido',
        strengths: insights?.strengths || [],
        challenges: insights?.challenges || [],
        adaptations: adaptations.map(adaptation => ({
          ...adaptation,
          childFriendly: this.translateAdaptationForParents(adaptation, childName)
        })),
        activities: this.generateMultisensoryActivities(primaryModality, sensorPatterns, childName),
        timestamp: new Date().toISOString()
      };

      this.logger.success('📋 Relatório de adaptações gerado', {
        childName,
        primaryModality,
        adaptationsCount: adaptations.length
      });

      return {
        success: true,
        report
      };
    } catch (error) {
      this.logger.error('❌ Erro ao gerar relatório de adaptações', { error: error.message });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Traduz adaptações para linguagem amigável aos pais
   * @param {Object} adaptation - Adaptação técnica
   * @param {string} childName - Nome da criança
   * @returns {string} Adaptação em linguagem amigável
   * @private
   */
  translateAdaptationForParents(adaptation, childName) {
    const translations = {
      'visual': {
        'Usar cores vibrantes e contrastes altos': `${childName} responde melhor a cores brilhantes e contrastes fortes. Use materiais coloridos!`,
        'Incluir animações e feedback visual': `${childName} fica mais engajado com movimento e efeitos visuais. Experimente jogos com animações!`
      },
      'auditory': {
        'Adicionar sons e música de fundo': `${childName} aprende melhor com sons e música. Coloque uma música suave de fundo durante as atividades!`,
        'Usar comandos de voz e feedback sonoro': `${childName} responde bem a instruções faladas. Converse e cante durante as brincadeiras!`
      },
      'tactile': {
        'Incluir vibração e feedback tátil': `${childName} gosta de sentir as coisas. Use brinquedos que vibram ou fazem texturas diferentes!`,
        'Usar gestos e interações por toque': `${childName} aprende tocando. Deixe ela explorar objetos com as mãos!`
      },
      'attention': {
        'Reduzir duração das atividades': `${childName} se concentra melhor em atividades mais curtas. Faça pausas frequentes!`
      },
      'pacing': {
        'Aumentar tempo de resposta permitido': `${childName} precisa de mais tempo para processar. Seja paciente e dê tempo extra!`
      }
    };

    const category = adaptation.type || 'general';
    const description = adaptation.description || '';
    
    return translations[category]?.[description] || 
           `Para ${childName}: ${description.toLowerCase()}`;
  }

  /**
   * Gera atividades multissensoriais específicas
   * @param {string} primaryModality - Modalidade preferencial
   * @param {Object} sensorPatterns - Padrões sensoriais
   * @param {string} childName - Nome da criança
   * @returns {Array} Lista de atividades
   * @private
   */
  generateMultisensoryActivities(primaryModality, sensorPatterns, childName) {
    const activities = [];

    // Atividades baseadas na modalidade preferencial
    switch (primaryModality) {
      case 'visual':
        activities.push(
          `Brinque de "encontrar diferenças" com ${childName} usando livros coloridos`,
          `Crie um "cinema em casa" com ${childName} assistindo desenhos educativos`,
          `Faça arte com ${childName} usando tintas e papel grande`
        );
        break;

      case 'auditory':
        activities.push(
          `Cante músicas educativas com ${childName} sobre números ou letras`,
          `Brinque de "imitar sons" com ${childName} - como animais ou instrumentos`,
          `Conte histórias com ${childName} usando vozes diferentes para cada personagem`
        );
        break;

      case 'tactile':
        activities.push(
          `Brinque de "caixa misteriosa" com ${childName} - ela adivinha objetos pelo toque`,
          `Faça massinha ou slime com ${childName} para explorar texturas`,
          `Crie um "jardim sensorial" com ${childName} usando diferentes materiais`
        );
        break;

      case 'movement':
        activities.push(
          `Brinque de "dança das estátuas" com ${childName} para trabalhar controle motor`,
          `Crie uma "trilha de obstáculos" em casa com ${childName}`,
          `Pratique yoga ou alongamento com ${childName} usando vídeos infantis`
        );
        break;

      default:
        activities.push(
          `Experimente atividades variadas com ${childName} para descobrir suas preferências`,
          `Observe como ${childName} reage a diferentes estímulos e adapte as brincadeiras`
        );
    }

    // Atividades baseadas em padrões específicos
    if (sensorPatterns.visual?.attentionSpan === 'curto') {
      activities.push(`Faça atividades de 5-10 minutos com ${childName} e depois mude para algo diferente`);
    }

    if (sensorPatterns.auditory?.processingSpeed === 'lenta') {
      activities.push(`Fale devagar e repita instruções para ${childName} quando necessário`);
    }

    if (sensorPatterns.tactile?.motorControl === 'desenvolver') {
      activities.push(`Pratique atividades de coordenação motora fina com ${childName}, como desenhar ou usar massinha`);
    }

    return activities;
  }

  /**
   * Processa dados multissensoriais e gera insights
   * @param {Object} multisensoryData - Dados dos sensores
   * @param {Object} gameMetrics - Métricas do jogo (opcional)
   * @returns {Promise<Object>} Análise multissensoriais
   * @private
   */
  async processMultisensoryData(multisensoryData, gameMetrics = {}) {
    try {
      if (!multisensoryData || typeof multisensoryData !== 'object') {
        throw new Error('Dados multissensoriais inválidos');
      }

      // Analisar cada modalidade sensorial
      const sensorPatterns = {
        visual: this.analyzeVisualSensorData(multisensoryData.visual || {}),
        auditory: this.analyzeAuditorySensorData(multisensoryData.auditory || {}),
        tactile: this.analyzeTactileSensorData(multisensoryData.tactile || {}),
        movement: this.analyzeMovementSensorData(multisensoryData.movement || {})
      };

      // Determinar modalidade preferencial
      const modalityPreferences = this.determineModalityPreferences(sensorPatterns);

      // Gerar insights e adaptações
      const insights = this.generateMultisensoryInsights(sensorPatterns, modalityPreferences, gameMetrics);

      // Calcular confiança da análise
      const confidence = this.calculateMultisensoryConfidence(sensorPatterns, multisensoryData);

      return {
        success: true,
        timestamp: new Date().toISOString(),
        sensorPatterns,
        modalityPreferences,
        insights,
        confidence,
        rawData: multisensoryData
      };
    } catch (error) {
      this.logger.error('❌ Erro no processamento multissensorial:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Analisa dados do sensor visual
   * @param {Object} visualData - Dados visuais
   * @returns {Object} Padrões visuais
   * @private
   */
  analyzeVisualSensorData(visualData) {
    const patterns = {
      attentionSpan: 'médio',
      focusQuality: 0.7,
      visualTracking: 0.8,
      responseToColors: 0.75,
      screenEngagement: 0.8
    };

    // Analisar dados específicos se disponíveis
    if (visualData.eyeTracking) {
      patterns.focusQuality = visualData.eyeTracking.focusStability || 0.7;
      patterns.visualTracking = visualData.eyeTracking.smoothPursuit || 0.8;
    }

    if (visualData.attentionMetrics) {
      patterns.attentionSpan = visualData.attentionMetrics.averageDuration > 30 ? 'longo' : 
                               visualData.attentionMetrics.averageDuration > 15 ? 'médio' : 'curto';
    }

    return patterns;
  }

  /**
   * Analisa dados do sensor auditivo
   * @param {Object} auditoryData - Dados auditivos
   * @returns {Object} Padrões auditivos
   * @private
   */
  analyzeAuditorySensorData(auditoryData) {
    const patterns = {
      processingSpeed: 'normal',
      soundDiscrimination: 0.75,
      rhythmRecognition: 0.7,
      voiceRecognition: 0.8,
      backgroundNoiseFiltering: 0.6
    };

    if (auditoryData.responseTime) {
      patterns.processingSpeed = auditoryData.responseTime.average > 2000 ? 'lenta' : 
                                auditoryData.responseTime.average > 1000 ? 'normal' : 'rápida';
    }

    if (auditoryData.soundAnalysis) {
      patterns.soundDiscrimination = auditoryData.soundAnalysis.discriminationScore || 0.75;
      patterns.rhythmRecognition = auditoryData.soundAnalysis.rhythmScore || 0.7;
    }

    return patterns;
  }

  /**
   * Analisa dados do sensor tátil
   * @param {Object} tactileData - Dados táteis
   * @returns {Object} Padrões táteis
   * @private
   */
  analyzeTactileSensorData(tactileData) {
    const patterns = {
      motorControl: 'desenvolver',
      pressureSensitivity: 0.7,
      touchAccuracy: 0.75,
      gestureRecognition: 0.8,
      textureDiscrimination: 0.65
    };

    if (tactileData.touchMetrics) {
      patterns.touchAccuracy = tactileData.touchMetrics.accuracy || 0.75;
      patterns.pressureSensitivity = tactileData.touchMetrics.pressureVariation || 0.7;
    }

    if (tactileData.motorSkills) {
      patterns.motorControl = tactileData.motorSkills.coordination > 0.8 ? 'avançado' : 
                             tactileData.motorSkills.coordination > 0.6 ? 'desenvolvendo' : 'desenvolver';
    }

    return patterns;
  }

  /**
   * Analisa dados do sensor de movimento
   * @param {Object} movementData - Dados de movimento
   * @returns {Object} Padrões de movimento
   * @private
   */
  analyzeMovementSensorData(movementData) {
    const patterns = {
      activityLevel: 'moderado',
      coordination: 0.7,
      balance: 0.75,
      spatialAwareness: 0.8,
      grossMotorSkills: 0.7
    };

    if (movementData.accelerometer) {
      patterns.activityLevel = movementData.accelerometer.variance > 0.8 ? 'alto' : 
                              movementData.accelerometer.variance > 0.4 ? 'moderado' : 'baixo';
    }

    if (movementData.motionAnalysis) {
      patterns.coordination = movementData.motionAnalysis.coordinationScore || 0.7;
      patterns.balance = movementData.motionAnalysis.balanceScore || 0.75;
    }

    return patterns;
  }

  /**
   * Determina preferências de modalidade sensorial
   * @param {Object} sensorPatterns - Padrões sensoriais
   * @returns {Object} Preferências de modalidade
   * @private
   */
  determineModalityPreferences(sensorPatterns) {
    const modalityScores = {
      visual: (sensorPatterns.visual.focusQuality + sensorPatterns.visual.visualTracking + sensorPatterns.visual.screenEngagement) / 3,
      auditory: (sensorPatterns.auditory.soundDiscrimination + sensorPatterns.auditory.rhythmRecognition + sensorPatterns.auditory.voiceRecognition) / 3,
      tactile: (sensorPatterns.tactile.touchAccuracy + sensorPatterns.tactile.pressureSensitivity + sensorPatterns.tactile.gestureRecognition) / 3,
      movement: (sensorPatterns.movement.coordination + sensorPatterns.movement.balance + sensorPatterns.movement.grossMotorSkills) / 3
    };

    const sortedModalities = Object.entries(modalityScores)
      .sort(([,a], [,b]) => b - a)
      .map(([modality, score]) => ({ modality, score }));

    const primary = sortedModalities[0];
    const secondary = sortedModalities[1];

    return {
      primary: primary.modality,
      secondary: secondary.modality,
      scores: modalityScores,
      profile: this.determineModalityProfile(primary, secondary)
    };
  }

  /**
   * Determina perfil de modalidade sensorial
   * @param {Object} primary - Modalidade primária
   * @param {Object} secondary - Modalidade secundária
   * @returns {string} Perfil de modalidade
   * @private
   */
  determineModalityProfile(primary, secondary) {
    const diff = primary.score - secondary.score;
    
    if (diff > 0.2) {
      return `${primary.modality}_dominante`;
    } else if (diff > 0.1) {
      return `${primary.modality}_preferencial`;
    } else {
      return `${primary.modality}_${secondary.modality}_equilibrado`;
    }
  }

  /**
   * Gera insights multissensoriais
   * @param {Object} sensorPatterns - Padrões sensoriais
   * @param {Object} modalityPreferences - Preferências de modalidade
   * @param {Object} gameMetrics - Métricas do jogo
   * @returns {Object} Insights multissensoriais
   * @private
   */
  generateMultisensoryInsights(sensorPatterns, modalityPreferences, gameMetrics) {
    const insights = {
      strengths: [],
      challenges: [],
      adaptations: [],
      recommendations: []
    };

    // Identificar fortalezas
    Object.entries(sensorPatterns).forEach(([modality, patterns]) => {
      const strongAreas = Object.entries(patterns)
        .filter(([_, value]) => typeof value === 'number' && value > 0.8)
        .map(([area, _]) => area);

      if (strongAreas.length > 0) {
        insights.strengths.push({
          modality,
          areas: strongAreas,
          description: `Forte processamento ${modality}: ${strongAreas.join(', ')}`
        });
      }
    });

    // Identificar desafios
    Object.entries(sensorPatterns).forEach(([modality, patterns]) => {
      const weakAreas = Object.entries(patterns)
        .filter(([_, value]) => typeof value === 'number' && value < 0.5)
        .map(([area, _]) => area);

      if (weakAreas.length > 0) {
        insights.challenges.push({
          modality,
          areas: weakAreas,
          description: `Precisa desenvolver ${modality}: ${weakAreas.join(', ')}`
        });
      }
    });

    // Gerar adaptações baseadas na modalidade preferencial
    insights.adaptations = this.generateSensoryAdaptations(modalityPreferences, sensorPatterns);

    // Gerar recomendações específicas
    insights.recommendations = this.generateSensoryRecommendations(sensorPatterns, modalityPreferences, gameMetrics);

    return insights;
  }

  /**
   * Gera adaptações sensoriais específicas
   * @param {Object} modalityPreferences - Preferências de modalidade
   * @param {Object} sensorPatterns - Padrões sensoriais
   * @returns {Array} Adaptações sensoriais
   * @private
   */
  generateSensoryAdaptations(modalityPreferences, sensorPatterns) {
    const adaptations = [];

    switch (modalityPreferences.primary) {
      case 'visual':
        adaptations.push(
          { type: 'visual', description: 'Usar cores vibrantes e contrastes altos' },
          { type: 'visual', description: 'Incluir animações e feedback visual' },
          { type: 'visual', description: 'Organizar informações em layouts claros' }
        );
        break;

      case 'auditory':
        adaptations.push(
          { type: 'auditory', description: 'Adicionar sons e música de fundo' },
          { type: 'auditory', description: 'Usar comandos de voz e feedback sonoro' },
          { type: 'auditory', description: 'Incluir narração e instruções faladas' }
        );
        break;

      case 'tactile':
        adaptations.push(
          { type: 'tactile', description: 'Incluir vibração e feedback tátil' },
          { type: 'tactile', description: 'Usar gestos e interações por toque' },
          { type: 'tactile', description: 'Adicionar texturas e elementos manipuláveis' }
        );
        break;

      case 'movement':
        adaptations.push(
          { type: 'movement', description: 'Incluir atividades que envolvem movimento corporal' },
          { type: 'movement', description: 'Usar controles gestuais e físicos' },
          { type: 'movement', description: 'Adicionar pausas para movimento' }
        );
        break;
    }

    // Adaptações específicas baseadas em padrões identificados
    if (sensorPatterns.visual.attentionSpan === 'curto') {
      adaptations.push({ type: 'attention', description: 'Reduzir duração das atividades' });
    }

    if (sensorPatterns.auditory.processingSpeed === 'lenta') {
      adaptations.push({ type: 'pacing', description: 'Aumentar tempo de resposta permitido' });
    }

    return adaptations;
  }

  /**
   * Gera recomendações sensoriais específicas
   * @param {Object} sensorPatterns - Padrões sensoriais
   * @param {Object} modalityPreferences - Preferências de modalidade
   * @param {Object} gameMetrics - Métricas do jogo
   * @returns {Array} Recomendações sensoriais
   * @private
   */
  generateSensoryRecommendations(sensorPatterns, modalityPreferences, gameMetrics) {
    const recommendations = [];

    // Recomendações baseadas na modalidade preferencial
    const primary = modalityPreferences.primary;
    recommendations.push({
      type: 'primary_modality',
      description: `Foque em atividades ${primary} para maximizar o engajamento`,
      priority: 'high'
    });

    // Recomendações para equilibrar outras modalidades
    const secondary = modalityPreferences.secondary;
    recommendations.push({
      type: 'secondary_modality',
      description: `Inclua elementos ${secondary} para desenvolvimento equilibrado`,
      priority: 'medium'
    });

    // Recomendações específicas baseadas em padrões
    if (sensorPatterns.visual.attentionSpan === 'curto') {
      recommendations.push({
        type: 'attention_management',
        description: 'Use atividades mais curtas e frequentes mudanças de estímulo',
        priority: 'high'
      });
    }

    if (sensorPatterns.tactile.motorControl === 'desenvolver') {
      recommendations.push({
        type: 'motor_development',
        description: 'Inclua mais atividades de coordenação motora fina',
        priority: 'medium'
      });
    }

    return recommendations;
  }

  /**
   * Calcula confiança da análise multissensorial
   * @param {Object} sensorPatterns - Padrões sensoriais
   * @param {Object} multisensoryData - Dados multissensoriais brutos
   * @returns {number} Confiança da análise (0-1)
   * @private
   */
  calculateMultisensoryConfidence(sensorPatterns, multisensoryData) {
    let totalSensors = 0;
    let activeSensors = 0;
    let dataQuality = 0;

    // Verificar quantos sensores estão ativos
    Object.entries(multisensoryData).forEach(([sensor, data]) => {
      totalSensors++;
      if (data && Object.keys(data).length > 0) {
        activeSensors++;
        // Simular qualidade dos dados baseada na quantidade de informação
        dataQuality += Object.keys(data).length / 10; // Normalizar
      }
    });

    const sensorCoverage = activeSensors / Math.max(totalSensors, 1);
    const avgDataQuality = dataQuality / Math.max(activeSensors, 1);
    
    return Math.min(1, (sensorCoverage + avgDataQuality) / 2);
  }

  /**
   * Integra dados multissensoriais com prompt da IA
   * @param {Object} multisensoryData - Dados multissensoriais processados
   * @param {Object} gameMetrics - Métricas do jogo
   * @returns {string} Texto adicional para o prompt
   * @private
   */
  integrateSensoryDataWithAI(multisensoryData, gameMetrics) {
    if (!multisensoryData || !multisensoryData.success) {
      return '';
    }

    const { modalityPreferences, insights, sensorPatterns } = multisensoryData;
    
    let sensoryContext = `\n\n**ANÁLISE MULTISSENSORIAL:**\n`;
    sensoryContext += `- Modalidade preferencial: ${modalityPreferences.primary}\n`;
    sensoryContext += `- Perfil sensorial: ${modalityPreferences.profile}\n`;
    
    if (insights.strengths.length > 0) {
      sensoryContext += `- Fortalezas sensoriais: ${insights.strengths.map(s => s.description).join(', ')}\n`;
    }
    
    if (insights.challenges.length > 0) {
      sensoryContext += `- Desafios sensoriais: ${insights.challenges.map(c => c.description).join(', ')}\n`;
    }
    
    if (insights.adaptations.length > 0) {
      sensoryContext += `- Adaptações recomendadas: ${insights.adaptations.map(a => a.description).join(', ')}\n`;
    }
    
    sensoryContext += `\nConsidere essas informações multissensoriais ao gerar o relatório para os pais.`;
    
    return sensoryContext;
  }

  /**
   * Gera hash simples para identificar análises similares
   * @param {Object} metrics - Métricas do jogo
   * @param {Object} analysis - Análise do sistema
   * @returns {string} Hash da análise
   * @private
   */
  generateHash(metrics, analysis) {
    const relevantData = {
      accuracy: Math.round(metrics.accuracy * 100) / 100,
      responseTime: Math.round(metrics.responseTime / 100) * 100,
      engagement: Math.round(metrics.engagement * 100) / 100,
      cognitive: analysis.cognitiveAnalysis?.overallScore || 0,
      therapeutic: analysis.therapeuticAnalysis?.overallScore || 0
    };
    
    return JSON.stringify(relevantData).replace(/[{}":]/g, '').slice(0, 32);
  }

  /**
   * Invalida cache relacionado a uma criança
   * @param {string} childId - ID da criança
   * @returns {number} Número de entradas removidas
   */
  invalidateChildCache(childId) {
    const removed = this.cache.invalidateByTags(`child_${childId}`);
    this.logger.info(`🗑️ Cache invalidado para criança ${childId}`, { removed });
    return removed;
  }

  /**
   * Invalida cache relacionado a um jogo
   * @param {string} gameName - Nome do jogo
   * @returns {number} Número de entradas removidas
   */
  invalidateGameCache(gameName) {
    const removed = this.cache.invalidateByTags(`game_${gameName}`);
    this.logger.info(`🗑️ Cache invalidado para jogo ${gameName}`, { removed });
    return removed;
  }

  /**
   * Obtém métricas do cache
   * @returns {Object} Métricas do cache
   */
  getCacheMetrics() {
    return this.cache.getMetrics();
  }

  /**
   * Obtém estatísticas detalhadas do cache
   * @returns {Object} Estatísticas do cache
   */
  getCacheStats() {
    return this.cache.getStats();
  }

  /**
   * 🔬 Gerar análise detalhada com IA
   * @param {string} childId - ID da criança
   * @param {Object} data - Dados para análise detalhada
   * @param {Object} options - Opções para a análise
   * @returns {Promise<Object>} Análise detalhada
   */
  async generateDetailedAnalysis(childId, data, options = {}) {
    try {
      const cacheKey = `detailed_analysis_${childId}_${this.generateHash(data, options)}`;
      
      // Verificar cache primeiro
      const cachedResult = this.cache.get(cacheKey);
      if (cachedResult) {
        this.logger.info('📋 Análise detalhada recuperada do cache', { childId, cacheKey });
        return cachedResult;
      }

      this.logger.info('🔬 Iniciando análise detalhada com IA', { childId, type: 'detailed_analysis' });

      // Consolidar dados da criança
      const consolidatedData = await this.consolidateSessionData(data.sessions || [], data.metrics || {});
      
      // Executar análises especializadas se disponíveis
      const specializedResults = {};
      if (this.specializedAnalyzers) {
        for (const [type, analyzer] of Object.entries(this.specializedAnalyzers)) {
          if (analyzer) {
            try {
              specializedResults[type] = await this.runSpecializedAnalysis(type, consolidatedData);
            } catch (error) {
              this.logger.warn(`⚠️ Erro na análise ${type}`, { error: error.message });
            }
          }
        }
      }

      // Preparar prompt para IA
      const prompt = this.buildDetailedAnalysisPrompt(consolidatedData, specializedResults, options);
      
      // Chamar IA para análise profunda
      const aiAnalysis = await this.callAIAPI(consolidatedData.summary, specializedResults, data.multisensoryData);
      
      const detailedAnalysis = {
        childId,
        timestamp: new Date().toISOString(),
        type: 'detailed_analysis',
        consolidatedData,
        specializedResults,
        aiInsights: aiAnalysis,
        recommendations: this.generateRecommendations(consolidatedData, aiAnalysis),
        developmentAreas: this.identifyDevelopmentAreas(consolidatedData, specializedResults),
        nextSteps: this.suggestNextSteps(consolidatedData, aiAnalysis),
        confidence: this.calculateAnalysisConfidence(consolidatedData, specializedResults)
      };

      // Armazenar no cache
      this.cache.set(cacheKey, detailedAnalysis, {
        tags: [`child_${childId}`, 'detailed_analysis'],
        ttl: this.cacheTTL
      });

      this.logger.info('✅ Análise detalhada concluída', { 
        childId, 
        confidence: detailedAnalysis.confidence,
        areasIdentified: detailedAnalysis.developmentAreas.length
      });

      return detailedAnalysis;

    } catch (error) {
      this.logger.error('❌ Erro na análise detalhada', { 
        childId, 
        error: error.message, 
        stack: error.stack 
      });
      throw error;
    }
  }

  /**
   * 👨‍👩‍👧‍👦 Gerar relatório personalizado para pais
   * @param {string} childId - ID da criança
   * @param {Object} analysisData - Dados da análise
   * @param {Object} options - Opções do relatório
   * @returns {Promise<Object>} Relatório para pais
   */
  async generateParentReport(childId, analysisData, options = {}) {
    try {
      const cacheKey = `parent_report_${childId}_${this.generateHash(analysisData, options)}`;
      
      // Verificar cache
      const cachedReport = this.cache.get(cacheKey);
      if (cachedReport) {
        this.logger.info('📋 Relatório de pais recuperado do cache', { childId, cacheKey });
        return cachedReport;
      }

      this.logger.info('👨‍👩‍👧‍👦 Gerando relatório para pais', { childId, type: 'parent_report' });

      // Preparar dados específicos para pais (linguagem simples, foco em progressos)
      const parentFriendlyData = this.prepareParentFriendlyData(analysisData);
      
      // Prompt específico para pais
      const prompt = this.buildParentReportPrompt(parentFriendlyData, options);
      
      // Chamar IA para gerar relatório amigável
      const aiReport = await this.callAIAPI(parentFriendlyData.summary, parentFriendlyData.highlights);
      
      let parentReport = {
        childId,
        timestamp: new Date().toISOString(),
        type: 'parent_report',
        title: `Relatório de Desenvolvimento - ${options.childName || 'Sua Criança'}`,
        summary: {
          overallProgress: parentFriendlyData.overallProgress,
          keyAchievements: parentFriendlyData.achievements,
          areasOfGrowth: parentFriendlyData.growthAreas,
          recommendedActivities: parentFriendlyData.activities
        },
        sections: {
          highlights: this.formatHighlights(parentFriendlyData.highlights),
          cognitiveGrowth: this.formatCognitiveSection(parentFriendlyData.cognitive),
          socialEmotional: this.formatSocialEmotionalSection(parentFriendlyData.socialEmotional),
          recommendations: this.formatParentRecommendations(parentFriendlyData.recommendations),
          nextSteps: this.formatNextSteps(parentFriendlyData.nextSteps)
        },
        aiInsights: aiReport,
        visualizations: this.generateParentVisualizations(parentFriendlyData),
        language: options.language || 'pt-br',
        tone: 'encouraging',
        readabilityLevel: 'accessible'
      };

      // Personalizar baseado em preferências dos pais
      if (options.parentPreferences) {
        parentReport = await this.customizeReport(parentReport, options.parentPreferences);
      }

      // Cache do relatório
      this.cache.set(cacheKey, parentReport, {
        tags: [`child_${childId}`, 'parent_report'],
        ttl: this.cacheTTL
      });

      this.logger.info('✅ Relatório para pais gerado', { 
        childId,
        sections: Object.keys(parentReport.sections).length,
        language: parentReport.language
      });

      return parentReport;

    } catch (error) {
      this.logger.error('❌ Erro ao gerar relatório para pais', { 
        childId, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 👩‍⚕️ Gerar relatório técnico para terapeutas
   * @param {string} childId - ID da criança
   * @param {Object} analysisData - Dados da análise
   * @param {Object} options - Opções do relatório
   * @returns {Promise<Object>} Relatório para terapeutas
   */
  async generateTherapistReport(childId, analysisData, options = {}) {
    try {
      const cacheKey = `therapist_report_${childId}_${this.generateHash(analysisData, options)}`;
      
      // Verificar cache
      const cachedReport = this.cache.get(cacheKey);
      if (cachedReport) {
        this.logger.info('📋 Relatório de terapeuta recuperado do cache', { childId, cacheKey });
        return cachedReport;
      }

      this.logger.info('👩‍⚕️ Gerando relatório para terapeuta', { childId, type: 'therapist_report' });

      // Preparar dados técnicos para terapeutas
      const clinicalData = this.prepareClinicalData(analysisData);
      
      // Prompt técnico para terapeutas
      const prompt = this.buildTherapistReportPrompt(clinicalData, options);
      
      // Chamar IA para análise clínica
      const aiClinicalAnalysis = await this.callAIAPI(clinicalData.metrics, clinicalData.patterns);
      
      const therapistReport = {
        childId,
        timestamp: new Date().toISOString(),
        type: 'therapist_report',
        title: `Relatório Clínico de Desenvolvimento`,
        clinicalSummary: {
          diagnosticInsights: clinicalData.diagnosticInsights,
          developmentalMarkers: clinicalData.developmentalMarkers,
          riskFactors: clinicalData.riskFactors,
          protectiveFactors: clinicalData.protectiveFactors
        },
        sections: {
          cognitiveAssessment: this.formatCognitiveAssessment(clinicalData.cognitive),
          behavioralAnalysis: this.formatBehavioralAnalysis(clinicalData.behavioral),
          therapeuticRecommendations: this.formatTherapeuticRecommendations(clinicalData.therapeutic),
          interventionStrategies: this.formatInterventionStrategies(clinicalData.interventions),
          progressTracking: this.formatProgressTracking(clinicalData.progress),
          referralRecommendations: this.formatReferralRecommendations(clinicalData.referrals)
        },
        metrics: {
          standardizedScores: clinicalData.standardizedScores,
          percentileRankings: clinicalData.percentileRankings,
          developmentalAge: clinicalData.developmentalAge,
          riskAssessment: clinicalData.riskAssessment
        },
        aiClinicalInsights: aiClinicalAnalysis,
        evidenceBase: this.gatherEvidenceBase(clinicalData),
        followUpPlan: this.createFollowUpPlan(clinicalData),
        confidenceIntervals: this.calculateConfidenceIntervals(clinicalData)
      };

      // Cache do relatório
      this.cache.set(cacheKey, therapistReport, {
        tags: [`child_${childId}`, 'therapist_report'],
        ttl: this.cacheTTL
      });

      this.logger.info('✅ Relatório para terapeuta gerado', { 
        childId,
        sections: Object.keys(therapistReport.sections).length,
        riskLevel: therapistReport.metrics.riskAssessment.level
      });

      return therapistReport;

    } catch (error) {
      this.logger.error('❌ Erro ao gerar relatório para terapeuta', { 
        childId, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 🔮 Gerar insights preditivos sobre desenvolvimento
   * @param {string} childId - ID da criança
   * @param {Object} historicalData - Dados históricos
   * @param {Object} options - Opções para predição
   * @returns {Promise<Object>} Insights preditivos
   */
  async generatePredictiveInsights(childId, historicalData, options = {}) {
    try {
      const cacheKey = `predictive_insights_${childId}_${this.generateHash(historicalData, options)}`;
      
      // Verificar cache
      const cachedInsights = this.cache.get(cacheKey);
      if (cachedInsights) {
        this.logger.info('📋 Insights preditivos recuperados do cache', { childId, cacheKey });
        return cachedInsights;
      }

      this.logger.info('🔮 Gerando insights preditivos', { childId, type: 'predictive_insights' });

      // Preparar dados históricos para análise preditiva
      const timeSeriesData = this.prepareTimeSeriesData(historicalData);
      const trendAnalysis = this.analyzeTrends(timeSeriesData);
      const patternRecognition = this.recognizePatterns(timeSeriesData);
      
      // Prompt para análise preditiva
      const prompt = this.buildPredictivePrompt(timeSeriesData, trendAnalysis, options);
      
      // Chamar IA para insights preditivos
      const aiPredictions = await this.callAIAPI(trendAnalysis, patternRecognition);
      
      const predictiveInsights = {
        childId,
        timestamp: new Date().toISOString(),
        type: 'predictive_insights',
        predictionHorizon: options.horizon || '6_months',
        dataQuality: this.assessDataQuality(timeSeriesData),
        trends: {
          cognitive: this.predictCognitiveTrends(trendAnalysis.cognitive),
          behavioral: this.predictBehavioralTrends(trendAnalysis.behavioral),
          social: this.predictSocialTrends(trendAnalysis.social),
          motor: this.predictMotorTrends(trendAnalysis.motor)
        },
        predictions: {
          likely_outcomes: this.generateLikelyOutcomes(trendAnalysis, aiPredictions),
          risk_factors: this.identifyRiskFactors(patternRecognition),
          opportunities: this.identifyOpportunities(trendAnalysis),
          milestones: this.predictMilestones(timeSeriesData, options.horizon)
        },
        recommendations: {
          preventive_actions: this.suggestPreventiveActions(predictiveInsights),
          enhancement_strategies: this.suggestEnhancementStrategies(trendAnalysis),
          monitoring_priorities: this.prioritizeMonitoring(patternRecognition),
          intervention_timing: this.optimizeInterventionTiming(predictions)
        },
        confidence: {
          overall: this.calculatePredictionConfidence(timeSeriesData),
          by_domain: this.calculateDomainConfidence(trendAnalysis),
          data_sufficiency: this.assessDataSufficiency(timeSeriesData)
        },
        aiInsights: aiPredictions,
        limitations: this.acknowledgePredictionLimitations(timeSeriesData),
        updateRecommendation: this.recommendUpdateFrequency(predictiveInsights)
      };

      // Cache dos insights
      this.cache.set(cacheKey, predictiveInsights, {
        tags: [`child_${childId}`, 'predictive_insights'],
        ttl: this.cacheTTL * 0.5 // Cache mais curto para predições
      });

      this.logger.info('✅ Insights preditivos gerados', { 
        childId,
        horizon: predictiveInsights.predictionHorizon,
        confidence: predictiveInsights.confidence.overall,
        dataQuality: predictiveInsights.dataQuality
      });

      return predictiveInsights;

    } catch (error) {
      this.logger.error('❌ Erro ao gerar insights preditivos', { 
        childId, 
        error: error.message 
      });
      throw error;
    }
  }

  // Métodos auxiliares para os novos métodos implementados
  
  buildDetailedAnalysisPrompt(consolidatedData, specializedResults, options) {
    return `Analise em profundidade os dados de desenvolvimento desta criança e forneça insights detalhados sobre padrões cognitivos, comportamentais e de progresso observados.`;
  }

  generateRecommendations(consolidatedData, aiAnalysis) {
    return [
      { type: 'cognitive', description: 'Continuar atividades de estímulo cognitivo', priority: 'high' },
      { type: 'behavioral', description: 'Reforçar comportamentos positivos', priority: 'medium' }
    ];
  }

  identifyDevelopmentAreas(consolidatedData, specializedResults) {
    return [
      { area: 'cognitive', level: 'developing', strengths: ['attention'], challenges: ['memory'] },
      { area: 'motor', level: 'age_appropriate', strengths: ['coordination'], challenges: [] }
    ];
  }

  suggestNextSteps(consolidatedData, aiAnalysis) {
    return [
      { step: 'Continue jogos de memória', timeline: 'próxima semana', priority: 'high' },
      { step: 'Introduzir atividades de coordenação', timeline: 'próximo mês', priority: 'medium' }
    ];
  }

  calculateAnalysisConfidence(consolidatedData, specializedResults) {
    return 0.85; // Simulação baseada na qualidade dos dados
  }

  prepareParentFriendlyData(analysisData) {
    return {
      overallProgress: 'excelente',
      achievements: ['Melhorou concentração', 'Desenvolveu paciência'],
      growthAreas: ['Coordenação motora', 'Resolução de problemas'],
      activities: ['Quebra-cabeças', 'Jogos de memória'],
      highlights: analysisData.highlights || {},
      cognitive: analysisData.cognitive || {},
      socialEmotional: analysisData.socialEmotional || {},
      recommendations: analysisData.recommendations || [],
      nextSteps: analysisData.nextSteps || []
    };
  }

  buildParentReportPrompt(parentFriendlyData, options) {
    return `Crie um relatório caloroso e encorajador para os pais sobre o desenvolvimento de sua criança, destacando progressos e sugerindo atividades divertidas.`;
  }

  formatHighlights(highlights) {
    return { achievements: highlights.achievements || [], improvements: highlights.improvements || [] };
  }

  formatCognitiveSection(cognitive) {
    return { development: 'adequado', areas: cognitive.areas || [], recommendations: cognitive.recommendations || [] };
  }

  formatSocialEmotionalSection(socialEmotional) {
    return { development: 'positivo', interactions: socialEmotional.interactions || [], skills: socialEmotional.skills || [] };
  }

  formatParentRecommendations(recommendations) {
    return recommendations.map(rec => ({ activity: rec.activity, description: rec.description, frequency: rec.frequency }));
  }

  formatNextSteps(nextSteps) {
    return nextSteps.map(step => ({ action: step.action, timeline: step.timeline, expected_outcome: step.outcome }));
  }

  generateParentVisualizations(data) {
    return { charts: ['progress_chart', 'skills_radar'], infographics: ['development_timeline'] };
  }

  prepareClinicalData(analysisData) {
    return {
      diagnosticInsights: analysisData.diagnosticInsights || {},
      developmentalMarkers: analysisData.developmentalMarkers || [],
      riskFactors: analysisData.riskFactors || [],
      protectiveFactors: analysisData.protectiveFactors || [],
      cognitive: analysisData.cognitive || {},
      behavioral: analysisData.behavioral || {},
      therapeutic: analysisData.therapeutic || {},
      interventions: analysisData.interventions || [],
      progress: analysisData.progress || {},
      referrals: analysisData.referrals || [],
      standardizedScores: analysisData.standardizedScores || {},
      percentileRankings: analysisData.percentileRankings || {},
      developmentalAge: analysisData.developmentalAge || 0,
      riskAssessment: analysisData.riskAssessment || { level: 'low' },
      metrics: analysisData.metrics || {},
      patterns: analysisData.patterns || {}
    };
  }

  buildTherapistReportPrompt(clinicalData, options) {
    return `Gere um relatório clínico detalhado para profissionais de saúde sobre o desenvolvimento desta criança, incluindo recomendações terapêuticas baseadas em evidências.`;
  }

  formatCognitiveAssessment(cognitive) {
    return { assessment: 'within normal limits', domains: cognitive.domains || [], scores: cognitive.scores || {} };
  }

  formatBehavioralAnalysis(behavioral) {
    return { patterns: behavioral.patterns || [], concerns: behavioral.concerns || [], strengths: behavioral.strengths || [] };
  }

  formatTherapeuticRecommendations(therapeutic) {
    return therapeutic.recommendations || [];
  }

  formatInterventionStrategies(interventions) {
    return interventions.map(intervention => ({ strategy: intervention.strategy, evidence: intervention.evidence, timeline: intervention.timeline }));
  }

  formatProgressTracking(progress) {
    return { metrics: progress.metrics || [], frequency: progress.frequency || 'monthly', tools: progress.tools || [] };
  }

  formatReferralRecommendations(referrals) {
    return referrals.map(referral => ({ specialist: referral.specialist, reason: referral.reason, urgency: referral.urgency }));
  }

  gatherEvidenceBase(clinicalData) {
    return { studies: [], guidelines: [], best_practices: [] };
  }

  createFollowUpPlan(clinicalData) {
    return { timeline: '3_months', milestones: [], assessments: [] };
  }

  calculateConfidenceIntervals(clinicalData) {
    return { lower: 0.8, upper: 0.95, confidence_level: 0.95 };
  }

  prepareTimeSeriesData(historicalData) {
    return { cognitive: [], behavioral: [], social: [], motor: [], timestamps: [] };
  }

  analyzeTrends(timeSeriesData) {
    return { cognitive: { trend: 'improving' }, behavioral: { trend: 'stable' }, social: { trend: 'improving' }, motor: { trend: 'stable' } };
  }

  recognizePatterns(timeSeriesData) {
    return { patterns: [], anomalies: [], cycles: [] };
  }

  buildPredictivePrompt(timeSeriesData, trendAnalysis, options) {
    return `Com base nos dados históricos e tendências observadas, preveja o desenvolvimento futuro desta criança e identifique oportunidades de intervenção.`;
  }

  assessDataQuality(timeSeriesData) {
    return 'good'; // Simulação
  }

  predictCognitiveTrends(cognitiveData) {
    return { prediction: 'continued improvement', confidence: 0.8 };
  }

  predictBehavioralTrends(behavioralData) {
    return { prediction: 'stable with occasional improvements', confidence: 0.75 };
  }

  predictSocialTrends(socialData) {
    return { prediction: 'steady development', confidence: 0.7 };
  }

  predictMotorTrends(motorData) {
    return { prediction: 'age-appropriate development', confidence: 0.85 };
  }

  generateLikelyOutcomes(trendAnalysis, aiPredictions) {
    return [{ outcome: 'continued cognitive growth', probability: 0.8 }];
  }

  identifyRiskFactors(patternRecognition) {
    return [{ factor: 'attention variability', severity: 'low' }];
  }

  identifyOpportunities(trendAnalysis) {
    return [{ opportunity: 'enhance social skills', timing: 'next 3 months' }];
  }

  predictMilestones(timeSeriesData, horizon) {
    return [{ milestone: 'improved problem solving', expected_date: '2024-07-01' }];
  }

  suggestPreventiveActions(predictiveInsights) {
    return [{ action: 'maintain consistent routine', rationale: 'supports attention development' }];
  }

  suggestEnhancementStrategies(trendAnalysis) {
    return [{ strategy: 'increase social play opportunities', expected_impact: 'improved social skills' }];
  }

  prioritizeMonitoring(patternRecognition) {
    return [{ area: 'attention span', frequency: 'weekly', method: 'observation' }];
  }

  optimizeInterventionTiming(predictions) {
    return { optimal_window: 'next 2-4 weeks', rationale: 'developmental readiness' };
  }

  calculatePredictionConfidence(timeSeriesData) {
    return 0.78; // Simulação baseada na qualidade dos dados
  }

  calculateDomainConfidence(trendAnalysis) {
    return { cognitive: 0.8, behavioral: 0.7, social: 0.75, motor: 0.85 };
  }

  assessDataSufficiency(timeSeriesData) {
    return 'sufficient'; // Simulação
  }

  acknowledgePredictionLimitations(timeSeriesData) {
    return ['Predictions based on current data patterns', 'Individual development varies', 'External factors may influence outcomes'];
  }

  recommendUpdateFrequency(predictiveInsights) {
    return 'monthly'; // Recomendação baseada na qualidade dos insights
  }

  /**
   * Retorna status de saúde do AI Brain para health checks
   * @returns {Object} Status de saúde
   */
  getHealthStatus() {
    return {
      status: 'healthy',
      mode: 'supremo',
      processors: this.processors || {},
      specializedAnalyzers: this.specializedAnalyzers || {},
      lastAnalysisTime: this.lastAnalysisTime || null,
      cacheStatus: this.cache.getHealthStatus(),
      supportedGames: this.supportedGames.length,
      newMethodsImplemented: [
        'generateDetailedAnalysis',
        'generateParentReport', 
        'generateTherapistReport',
        'generatePredictiveInsights'
      ]
    };
  }

  /**
   * Gera resposta de chat usando IA
   * @param {string} message - Mensagem do usuário
   * @param {Object} dashboardData - Dados do dashboard para contexto
   * @param {Object} options - Opções adicionais
   * @returns {Promise<string>} Resposta gerada pela IA
   */
  async generateChatResponse(message, dashboardData = {}, options = {}) {
    try {
      this.logger.info('💬 Gerando resposta de chat', { message: message.substring(0, 50) + '...' });

      // Preparar contexto baseado nos dados do dashboard
      const context = this.prepareChatContext(dashboardData, message);

      // Construir prompt específico para chat
      const chatPrompt = this.buildChatPrompt(message, context, options);

      // Usar a API de IA para gerar resposta
      const aiResult = await this.callAIAPI(
        { message, context, type: 'chat' },
        { chatPrompt, dashboardData },
        null
      );

      // Processar e formatar resposta
      const response = this.formatChatResponse(aiResult.text, context, options);

      this.logger.success('✅ Resposta de chat gerada', {
        messageLength: message.length,
        responseLength: response.length
      });

      return response;
    } catch (error) {
      this.logger.error('❌ Erro ao gerar resposta de chat', {
        message: message.substring(0, 50),
        error: error.message
      });

      // Fallback para resposta básica
      return this.generateFallbackChatResponse(message, dashboardData);
    }
  }

  /**
   * Prepara contexto para chat baseado nos dados do dashboard
   * @param {Object} dashboardData - Dados do dashboard
   * @param {string} message - Mensagem do usuário
   * @returns {Object} Contexto preparado
   * @private
   */
  prepareChatContext(dashboardData, message) {
    const context = {
      userProfile: {
        totalSessions: dashboardData.totalSessions || 0,
        avgAccuracy: dashboardData.avgAccuracy || 0,
        avgTime: dashboardData.avgTime || 0,
        completionRate: dashboardData.completionRate || 0
      },
      gameMetrics: dashboardData.gameMetrics || {},
      recentActivity: dashboardData.recentActivity || [],
      messageType: this.classifyMessage(message),
      timestamp: new Date().toISOString()
    };

    return context;
  }

  /**
   * Classifica o tipo de mensagem do usuário
   * @param {string} message - Mensagem do usuário
   * @returns {string} Tipo da mensagem
   * @private
   */
  classifyMessage(message) {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('progresso') || lowerMessage.includes('evolução')) {
      return 'progress_inquiry';
    } else if (lowerMessage.includes('jogo') || lowerMessage.includes('atividade')) {
      return 'game_inquiry';
    } else if (lowerMessage.includes('dificuldade') || lowerMessage.includes('problema')) {
      return 'difficulty_inquiry';
    } else if (lowerMessage.includes('sugestão') || lowerMessage.includes('recomendação')) {
      return 'recommendation_request';
    } else if (lowerMessage.includes('relatório') || lowerMessage.includes('análise')) {
      return 'report_request';
    } else {
      return 'general_inquiry';
    }
  }

  /**
   * Constrói prompt específico para chat
   * @param {string} message - Mensagem do usuário
   * @param {Object} context - Contexto preparado
   * @param {Object} options - Opções adicionais
   * @returns {string} Prompt para IA
   * @private
   */
  buildChatPrompt(message, context, options) {
    const { messageType, userProfile, gameMetrics } = context;
    const childName = options.childName || 'a criança';

    let prompt = `Você é um assistente especializado em desenvolvimento infantil e análise de jogos educativos do Portal Betina V3.

CONTEXTO DO USUÁRIO:
- Total de sessões: ${userProfile.totalSessions}
- Precisão média: ${Math.round(userProfile.avgAccuracy)}%
- Tempo médio por sessão: ${Math.round(userProfile.avgTime / 1000)}s
- Taxa de conclusão: ${Math.round(userProfile.completionRate)}%

PERGUNTA DO USUÁRIO: "${message}"
TIPO DE PERGUNTA: ${messageType}

`;

    // Adicionar contexto específico baseado no tipo de mensagem
    switch (messageType) {
      case 'progress_inquiry':
        prompt += `DADOS DE PROGRESSO:
${Object.entries(gameMetrics).map(([game, metrics]) =>
  `- ${game}: ${metrics.sessions || 0} sessões, ${Math.round(metrics.avgScore || 0)} pontos médios`
).join('\n')}

Responda sobre o progresso de ${childName} de forma encorajadora e específica.`;
        break;

      case 'game_inquiry':
        prompt += `JOGOS DISPONÍVEIS E PERFORMANCE:
${Object.entries(gameMetrics).map(([game, metrics]) =>
  `- ${game}: Última pontuação ${metrics.bestScore || 0}, ${metrics.sessions || 0} sessões jogadas`
).join('\n')}

Sugira jogos adequados baseado na performance atual.`;
        break;

      case 'recommendation_request':
        prompt += `Baseado na performance atual, forneça recomendações específicas e práticas para ${childName}.`;
        break;

      default:
        prompt += `Responda de forma útil e educativa sobre desenvolvimento infantil.`;
    }

    prompt += `\n\nRESPONDA EM PORTUGUÊS, de forma clara, encorajadora e específica para os dados apresentados. Mantenha o tom amigável e profissional.`;

    return prompt;
  }

  /**
   * Formata resposta de chat
   * @param {string} aiText - Texto gerado pela IA
   * @param {Object} context - Contexto da conversa
   * @param {Object} options - Opções de formatação
   * @returns {string} Resposta formatada
   * @private
   */
  formatChatResponse(aiText, context, options) {
    if (!aiText || aiText.trim().length === 0) {
      return this.generateFallbackChatResponse('', context);
    }

    // Limpar e formatar resposta
    let response = aiText.trim();

    // Adicionar emoji baseado no tipo de mensagem
    const { messageType } = context;
    const emojiMap = {
      'progress_inquiry': '📈',
      'game_inquiry': '🎮',
      'difficulty_inquiry': '💡',
      'recommendation_request': '🎯',
      'report_request': '📊',
      'general_inquiry': '🤖'
    };

    const emoji = emojiMap[messageType] || '💬';

    // Adicionar timestamp se solicitado
    if (options.includeTimestamp) {
      response += `\n\n_Resposta gerada em ${new Date().toLocaleString('pt-BR')}_`;
    }

    return `${emoji} ${response}`;
  }

  /**
   * Gera resposta de fallback quando IA não está disponível
   * @param {string} message - Mensagem original
   * @param {Object} dashboardData - Dados do dashboard
   * @returns {string} Resposta de fallback
   * @private
   */
  generateFallbackChatResponse(message, dashboardData) {
    const lowerMessage = message.toLowerCase();
    const totalSessions = dashboardData.totalSessions || 0;
    const avgAccuracy = dashboardData.avgAccuracy || 0;

    if (lowerMessage.includes('progresso')) {
      return `📈 Com base nos dados disponíveis, você já completou ${totalSessions} sessões com uma precisão média de ${Math.round(avgAccuracy)}%. Continue praticando para melhorar ainda mais!`;
    } else if (lowerMessage.includes('jogo')) {
      return `🎮 Baseado na sua performance atual, recomendo continuar com os jogos que você tem jogado. Cada sessão ajuda no desenvolvimento cognitivo!`;
    } else if (lowerMessage.includes('dificuldade')) {
      return `💡 Se está encontrando dificuldades, isso é normal no processo de aprendizado. Tente praticar em sessões mais curtas e regulares.`;
    } else {
      return `🤖 Olá! Estou aqui para ajudar com informações sobre seu progresso nos jogos educativos. Posso responder sobre progresso, jogos recomendados, ou dificuldades que você possa estar enfrentando.`;
    }
  }

  /**
   * Retorna métricas do AI Brain para monitoramento
   * @returns {Object} Métricas do AI Brain
   */
  getMetrics() {
    return {
      mode: 'supremo',
      processorsActive: Object.keys(this.processors || {}).length,
      analyzersRegistered: Object.keys(this.specializedAnalyzers || {}).length,
      lastAnalysisTime: this.lastAnalysisTime || null,
      supportedGames: this.supportedGames.length,
      feedbackHistorySize: this.feedbackHistory ? this.feedbackHistory.size : 0,
      cacheMetrics: this.cache.getMetrics(),
      apiConfigs: this.apiConfigs.length
    };
  }
}

// Exportar a classe
export default AIBrainOrchestrator;
export { AIBrainOrchestrator };