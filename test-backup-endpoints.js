/**
 * @file test-backup-endpoints.js
 * @description Script para testar todos os endpoints da API de backup
 * @version 1.0.0
 */

const BASE_URL = 'http://localhost:3000/api/backup'

// Função para fazer requisições HTTP
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    
    return {
      status: response.status,
      ok: response.ok,
      data: data
    }
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    }
  }
}

// Função para exibir resultados dos testes
function displayResult(testName, result) {
  const status = result.ok ? '✅ PASSOU' : '❌ FALHOU'
  console.log(`\n${status} ${testName}`)
  console.log(`Status: ${result.status}`)
  
  if (result.ok) {
    console.log('Resposta:', JSON.stringify(result.data, null, 2))
  } else {
    console.log('Erro:', result.error || result.data)
  }
  console.log('-'.repeat(60))
}

// Testes dos endpoints
async function testEndpoints() {
  console.log('🧪 INICIANDO TESTES DOS ENDPOINTS DE BACKUP')
  console.log('=' .repeat(60))

  // 1. Teste da rota de teste
  console.log('\n1️⃣ TESTANDO: GET /api/backup/test')
  const testResult = await makeRequest(`${BASE_URL}/test`)
  displayResult('Rota de Teste', testResult)

  // 2. Teste do status de backup
  console.log('\n2️⃣ TESTANDO: GET /api/backup/status/:userId')
  const statusResult = await makeRequest(`${BASE_URL}/status/user_demo`)
  displayResult('Status de Backup', statusResult)

  // 3. Teste de geração de backup (sem opções)
  console.log('\n3️⃣ TESTANDO: POST /api/backup/user-data (sem opções)')
  const backupNoOptionsResult = await makeRequest(`${BASE_URL}/user-data`, {
    method: 'POST',
    body: JSON.stringify({
      userId: 'user_demo'
    })
  })
  displayResult('Backup sem Opções', backupNoOptionsResult)

  // 4. Teste de geração de backup (com todas as opções)
  console.log('\n4️⃣ TESTANDO: POST /api/backup/user-data (com todas as opções)')
  const backupAllOptionsResult = await makeRequest(`${BASE_URL}/user-data`, {
    method: 'POST',
    body: JSON.stringify({
      userId: 'user_demo',
      options: {
        userProfiles: true,
        gameProgress: true,
        gameMetrics: true,
        sessionData: true,
        accessibilitySettings: true,
        preferences: true
      }
    })
  })
  displayResult('Backup com Todas as Opções', backupAllOptionsResult)

  // 5. Teste de geração de backup (sem userId - deve falhar)
  console.log('\n5️⃣ TESTANDO: POST /api/backup/user-data (sem userId - deve falhar)')
  const backupNoUserResult = await makeRequest(`${BASE_URL}/user-data`, {
    method: 'POST',
    body: JSON.stringify({
      options: {
        userProfiles: true
      }
    })
  })
  displayResult('Backup sem UserId (Erro Esperado)', backupNoUserResult)

  // 6. Teste de importação de backup (dados válidos)
  console.log('\n6️⃣ TESTANDO: POST /api/backup/import (dados válidos)')
  const sampleBackupData = {
    version: '3.1.0',
    exportDate: new Date().toISOString(),
    userId: 'user_demo',
    data: {
      userProfiles: { id: 'user_demo', name: 'Test User' },
      gameMetrics: { ColorMatch: { sessions: 10, avgScore: 80 } }
    },
    metadata: {
      source: 'test',
      totalItems: 2,
      categories: ['userProfiles', 'gameMetrics']
    }
  }

  const importValidResult = await makeRequest(`${BASE_URL}/import`, {
    method: 'POST',
    body: JSON.stringify({
      userId: 'user_demo',
      backupData: sampleBackupData,
      options: {
        userProfiles: true,
        gameMetrics: true
      }
    })
  })
  displayResult('Importação Válida', importValidResult)

  // 7. Teste de importação de backup (versão inválida - deve falhar)
  console.log('\n7️⃣ TESTANDO: POST /api/backup/import (versão inválida - deve falhar)')
  const invalidBackupData = {
    version: '2.0.0', // Versão inválida
    exportDate: new Date().toISOString(),
    userId: 'user_demo',
    data: {}
  }

  const importInvalidResult = await makeRequest(`${BASE_URL}/import`, {
    method: 'POST',
    body: JSON.stringify({
      userId: 'user_demo',
      backupData: invalidBackupData
    })
  })
  displayResult('Importação Versão Inválida (Erro Esperado)', importInvalidResult)

  // 8. Teste de importação de backup (sem dados - deve falhar)
  console.log('\n8️⃣ TESTANDO: POST /api/backup/import (sem dados - deve falhar)')
  const importNoDataResult = await makeRequest(`${BASE_URL}/import`, {
    method: 'POST',
    body: JSON.stringify({
      userId: 'user_demo'
    })
  })
  displayResult('Importação sem Dados (Erro Esperado)', importNoDataResult)

  // Resumo dos testes
  console.log('\n📊 RESUMO DOS TESTES')
  console.log('=' .repeat(60))
  console.log('✅ Testes que devem PASSAR: 1, 2, 3, 4, 6')
  console.log('❌ Testes que devem FALHAR: 5, 7, 8')
  console.log('\nSe todos os resultados estão conforme esperado, a API está funcionando corretamente!')
}

// Executar os testes
testEndpoints().catch(console.error)
